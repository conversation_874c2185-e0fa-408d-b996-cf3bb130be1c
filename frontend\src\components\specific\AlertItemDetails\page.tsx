"use client"

import { <PERSON>, <PERSON>, Spin<PERSON>, <PERSON>, Card<PERSON>ody, Divider } from "@nextui-org/react"
import { <PERSON>, MapPin, User, <PERSON><PERSON><PERSON><PERSON><PERSON>, Heart, Shield } from "lucide-react"
import { useEffect, useState } from "react"
import { fetcher } from "@/lib/utils"

type Props = {
  id: string
}

type AlertRequest = {
  created_at: string
  description: string
  emergency_type: string
  id: number
  images: { id: number; src: string }[]
  location: string
  user_first_name: string
  user_last_name: string
}

// Utility functions for formatting and styling
const getEmergencyTypeLabel = (type: string): string => {
  switch (type) {
    case "O": return "طلب مساعدة"
    case "M": return "طبية"
    case "D": return "خطر"
    default: return "غير محدد"
  }
}

const getEmergencyTypeColor = (type: string) => {
  switch (type) {
    case "O": return "primary"
    case "M": return "success"
    case "D": return "danger"
    default: return "default"
  }
}

const getEmergencyTypeIcon = (type: string) => {
  switch (type) {
    case "O": return Heart
    case "M": return Shield
    case "D": return AlertTriangle
    default: return AlertTriangle
  }
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  }).format(date)
}

export function AlertItemDetails({ id }: Props) {
  const [isLoading, setIsLoading] = useState(true)
  const [data, setData] = useState<AlertRequest | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    setIsLoading(true)
    setError(null)

    fetcher(`/emergency/${id}/`, null, "GET")
      .then((res) => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`)
        }
        return res.json()
      })
      .then((data) => {
        setData(data as AlertRequest)
      })
      .catch((err) => {
        console.error("Error fetching alert details:", err)
        setError("حدث خطأ أثناء تحميل تفاصيل الإشعار")
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [id])

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4" dir="rtl">
        <Spinner size="lg" color="primary" />
        <p className="text-gray-600">جارى تحميل تفاصيل الإشعار...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4" dir="rtl">
        <AlertTriangle className="w-16 h-16 text-red-500" />
        <p className="text-red-600 text-center">{error}</p>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4" dir="rtl">
        <AlertTriangle className="w-16 h-16 text-gray-400" />
        <p className="text-gray-600 text-center">لم يتم العثور على تفاصيل الإشعار</p>
      </div>
    )
  }

  const EmergencyIcon = getEmergencyTypeIcon(data.emergency_type)

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6" dir="rtl">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <div className="flex justify-center items-center gap-3">
          <EmergencyIcon className="w-8 h-8 text-gray-600" />
          <h1 className="text-2xl font-bold text-gray-800">تفاصيل إشعار الطوارئ</h1>
        </div>

        <Chip
          color={getEmergencyTypeColor(data.emergency_type) as any}
          size="lg"
          variant="flat"
          className="text-lg px-4 py-2"
        >
          {getEmergencyTypeLabel(data.emergency_type)}
        </Chip>
      </div>

      {/* Main Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* User Information */}
        <Card className="shadow-md">
          <CardBody className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <User className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold">معلومات المبلغ</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">الاسم:</span>
                <span className="font-medium">{data.user_first_name} {data.user_last_name}</span>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Location & Time Information */}
        <Card className="shadow-md">
          <CardBody className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <MapPin className="w-6 h-6 text-green-600" />
              <h3 className="text-lg font-semibold">معلومات الموقع والوقت</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-start">
                <span className="text-gray-600">الموقع:</span>
                <span className="font-medium text-left">{data.location}</span>
              </div>
              <Divider />
              <div className="flex justify-between items-start">
                <span className="text-gray-600">وقت الإبلاغ:</span>
                <div className="flex items-center gap-2 text-left">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">{formatDate(data.created_at)}</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Description Section */}
      <Card className="shadow-md">
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold mb-4">وصف الحالة</h3>
          <p className="text-gray-700 leading-relaxed text-justify bg-gray-50 p-4 rounded-lg">
            {data.description}
          </p>
        </CardBody>
      </Card>

      {/* Images Section */}
      {data.images && data.images.length > 0 && (
        <Card className="shadow-md">
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold mb-4">الصور المرفقة ({data.images.length})</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {data.images.map(({ id, src }, index) => (
                <div key={id} className="relative group">
                  <Image
                    src={src || "/placeholder.svg?height=240&width=240"}
                    alt={`صورة ${index + 1}`}
                    width={300}
                    height={200}
                    className="object-cover rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                    <span className="text-white opacity-0 group-hover:opacity-100 transition-opacity">
                      انقر للتكبير
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  )
}
