(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[27],{46514:(e,s,t)=>{Promise.resolve().then(t.bind(t,69513))},69513:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S});var a=t(95155),l=t(12115),r=t(28920),c=t(56657),n=t(87036),i=t(66146),d=t(27290),m=t(54736),x=t(94554),o=t(91768),h=t(1243),u=t(51976),g=t(75525),j=t(14186),p=t(71007),N=t(4516),y=t(59434),f=t(73906),b=t(4766);let v=[{key:"all",label:"جميع الإشعارات",icon:h.A},{key:"O",label:"طلب مساعدة",icon:u.A},{key:"M",label:"طبية",icon:g.A},{key:"D",label:"خطر",icon:h.A}],w=e=>{switch(e){case"O":return"طلب مساعدة";case"M":return"طبية";case"D":return"خطر";default:return"غير محدد"}},_=e=>{switch(e){case"O":return"primary";case"M":return"success";case"D":return"danger";default:return"default"}},k=e=>new Date(e).toLocaleDateString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});function S(){let[e,s]=(0,l.useState)([]),[t,u]=(0,l.useState)(!0),[g,S]=(0,l.useState)(null),[A,E]=(0,l.useState)(1),[O,z]=(0,l.useState)(1),[C,D]=(0,l.useState)("all"),[M,T]=(0,l.useState)(0),G=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all";try{u(!0),S(null);let a="/emergency/?page=".concat(e,"&page_size=").concat(12);"all"!==t&&(a+="&emergency_type=".concat(t));let l=await (0,y.G)(a,null,"GET");if(!l.ok)throw Error("خطأ في الشبكة: ".concat(l.status));let r=await l.text(),c=r?JSON.parse(r):{count:0,results:[],next:null,previous:null};s(c.results||[]),T(c.count||0),z(Math.ceil((c.count||0)/12))}catch(e){console.error("Error fetching notifications:",e),S("حدث خطأ أثناء تحميل الإشعارات. يرجى المحاولة مرة أخرى."),s([])}finally{u(!1)}};(0,l.useEffect)(()=>{G(A,C)},[A,C]);let F=e=>{D(e),E(1)};return t&&0===e.length?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(r.o,{size:"lg"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"جارٍ تحميل الإشعارات..."})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-center mb-2",children:"الإشعارات السابقة"}),(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["عرض جميع إشعارات الطوارئ المرسلة سابقاً (",M," إشعار)"]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(c.r,{selectedKey:C,onSelectionChange:e=>F(e),className:"w-full",classNames:{tabList:"w-full",tab:"flex-1"},children:v.map(e=>{let s=e.icon;return(0,a.jsx)(n.i,{title:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.label})]})},e.key)})})}),g&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsx)("p",{className:"text-red-600 text-center",children:g}),(0,a.jsx)(i.T,{color:"danger",variant:"light",className:"mt-2 mx-auto block",onPress:()=>G(A,C),children:"إعادة المحاولة"})]}),0!==e.length||t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8",children:e.map(e=>(0,a.jsxs)(f.aF,{children:[(0,a.jsx)(f.g6,{children:(0,a.jsx)(d.Z,{className:"hover:shadow-lg transition-shadow cursor-pointer h-full",children:(0,a.jsxs)(m.U,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,a.jsx)(x.R,{color:_(e.emergency_type),size:"sm",variant:"flat",children:w(e.emergency_type)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,a.jsx)(j.A,{className:"w-3 h-3"}),k(e.created_at)]})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(p.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.user_first_name," ",e.user_last_name]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{children:e.location})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 line-clamp-3",children:e.description}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,a.jsx)(i.T,{size:"sm",color:"primary",variant:"light",className:"w-full",children:"عرض التفاصيل"})})]})})}),(0,a.jsx)(f.cw,{children:(0,a.jsx)(f.$m,{children:(0,a.jsx)(b.A,{id:e.id.toString()})})})]},e.id))}),O>1&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(o.T,{total:O,page:A,onChange:e=>{E(e)},showControls:!0,className:"gap-2",classNames:{wrapper:"gap-0 overflow-visible h-8",item:"w-8 h-8 text-small rounded-none bg-transparent",cursor:"bg-primary text-white font-bold"}})}),t&&e.length>0&&(0,a.jsx)("div",{className:"flex justify-center mt-4",children:(0,a.jsx)(r.o,{size:"sm"})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"لا توجد إشعارات"}),(0,a.jsx)("p",{className:"text-gray-500",children:"all"===C?"لم يتم العثور على أي إشعارات طوارئ":'لم يتم العثور على إشعارات من نوع "'.concat(w(C),'"')})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[146,688,829,686,254,170,421,377,333,441,684,358],()=>s(46514)),_N_E=e.O()}]);