"use client";

import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { MapPin } from "lucide-react";
import { useState } from "react";
import { Button, Link, Select, SelectItem, Textarea } from "@nextui-org/react";
import {
  type EmergencyApplicationSchemaType,
  emergencyApplicationSchema,
} from "@/schemas/application";
import { FileUpload } from "@/components/ui/file-upload";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

type Params = {
  token: string;
};

const locations = [
  "القدس",
  "رام الله",
  "بيت لحم",
  "الخليل",
  "نابلس",
  "جنين",
  "طولكرم",
  "قلقيلية",
  "أريحا",
];

const assistanceTypes = [
  { value: "M", text: "طبية" },
  { value: "O", text: "إغاثة" },
  { value: "D", text: "خطر" },
];

export function EmergencyForm({ token }: Params) {
  const router = useRouter();
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EmergencyApplicationSchemaType>({
    resolver: zodResolver(emergencyApplicationSchema),
    defaultValues: {
      location: "",
      description: "",
      images: [],
    },
  });

  async function onSubmit(data: EmergencyApplicationSchemaType) {
    try {
      const formData = new FormData();

      data.images?.forEach((file: File) => {
        formData.append(`images`, file);
      });

      formData.append("location", data.location);
      formData.append("emergency_type", data.emergency_type);
      formData.append("description", data.description);

      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "https://api.example.com";

      const res = await fetch(`${backendUrl}/emergency/create/`, {
        method: "POST",
        body: formData,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (res.status === 201) {
        toast.success("تم إرسال الطلب بنجاح");
        router.refresh();
        router.push("/");
      } else {
        const data = await res.json();
        console.error("Error submitting form:", data);
        toast.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة");
    }
  }

  const handleImageChange = (files: File[]) => {
    if (!files || files.length <= 0) return;

    const newImages = Array.from(files).filter((file) =>
      file.type.startsWith("image/")
    );

    setSelectedImages((prevImages) => {
      const updatedImages = [...prevImages, ...newImages];
      setValue("images", updatedImages); // Set the accumulated images
      return updatedImages;
    });
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">أرسل إشعار للطوارئ</h1>
        <MapPin className="w-6 h-6 text-blue-500" />
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2">
          <label className="text-sm font-medium">حدد موقعك (إجباري) *</label>
          <Controller
            name="location"
            control={control}
            render={({ field }) => (
              <Select
                label="من فضلك اختر موقعك"
                {...field}
                errorMessage={errors.location?.message}
                isInvalid={!!errors.location}
              >
                {locations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </Select>
            )}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">نوع المساعدة (إجباري) *</label>
          <Controller
            name="emergency_type"
            control={control}
            render={({ field }) => (
              <Select
                label="من فضلك اختر نوع المساعدة"
                {...field}
                errorMessage={errors.emergency_type?.message}
                isInvalid={!!errors.emergency_type}
              >
                {assistanceTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.text}
                  </SelectItem>
                ))}
              </Select>
            )}
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">
            ارفق صور للحالة (إجباري) *
          </label>

          <FileUpload isMultiple onChange={handleImageChange} />

          {/* Image Preview */}
          {selectedImages.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">الصور المحددة ({selectedImages.length}):</p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {selectedImages.map((file, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`صورة ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg border"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const newImages = selectedImages.filter((_, i) => i !== index);
                        setSelectedImages(newImages);
                        setValue("images", newImages);
                      }}
                      className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-red-600"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {errors.images && (
            <p className="text-xs text-danger">{errors.images.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">وصف الحالة (إجباري) *</label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                placeholder="من فضلك اكتب وصف الحالة"
                minRows={25}
                {...field}
                errorMessage={errors.description?.message}
                isInvalid={!!errors.description}
              />
            )}
          />
        </div>

        <div className="flex justify-between items-center gap-2">
          <Button
            as={Link}
            href="/"
            type="button"
            variant="bordered"
            color="default"
          >
            إلغاء
          </Button>
          <Button type="submit" color="primary" isLoading={isSubmitting}>
            أرسل الطلب
          </Button>
        </div>
      </form>
    </div>
  );
}
