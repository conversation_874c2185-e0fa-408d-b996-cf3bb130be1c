"use client"

import type React from "react"

import { Button } from "@nextui-org/button"
import { useEffect, useState } from "react"

type ErrorBoundaryProps = {
  children: React.ReactNode
}

export default function ErrorBoundary({ children }: ErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error("Error caught by error boundary:", event.error)
      setHasError(true)
      // Prevent the error from propagating
      event.preventDefault()
    }

    window.addEventListener("error", handleError)

    return () => {
      window.removeEventListener("error", handleError)
    }
  }, [])

  if (hasError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] p-6 text-center">
        <h2 className="text-2xl font-bold mb-4">حدث خطأ غير متوقع</h2>
        <p className="mb-6 text-gray-600">نعتذر عن هذا الخطأ. يرجى تحديث الصفحة أو العودة إلى الصفحة الرئيسية.</p>
        <div className="flex gap-4">
          <Button color="primary" onClick={() => window.location.reload()}>
            تحديث الصفحة
          </Button>
          <Button variant="bordered" onClick={() => (window.location.href = "/")}>
            العودة للصفحة الرئيسية
          </Button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
