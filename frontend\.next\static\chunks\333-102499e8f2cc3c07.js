"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[333],{4766:(e,t,s)=>{s.d(t,{A:()=>w});var r=s(95155),a=s(28920),l=s(94554),n=s(27290),c=s(54736),i=s(64924),o=s(20340),d=s(51976),x=s(75525),m=s(1243),u=s(71007),h=s(4516),p=s(14186),f=s(12115),g=s(59434);let j=e=>{switch(e){case"O":return"طلب مساعدة";case"M":return"طبية";case"D":return"خطر";default:return"غير محدد"}},y=e=>{switch(e){case"O":return"primary";case"M":return"success";case"D":return"danger";default:return"default"}},v=e=>{switch(e){case"O":return d.A;case"M":return x.A;default:return m.A}},N=e=>{let t=new Date(e);return new Intl.DateTimeFormat("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0}).format(t)};function w(e){let{id:t}=e,[s,d]=(0,f.useState)(!0),[x,w]=(0,f.useState)(null),[b,k]=(0,f.useState)(null);if((0,f.useEffect)(()=>{d(!0),k(null),(0,g.G)("/emergency/".concat(t,"/"),null,"GET").then(e=>{if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));return e.json()}).then(e=>{w(e)}).catch(e=>{console.error("Error fetching alert details:",e),k("حدث خطأ أثناء تحميل تفاصيل الإشعار")}).finally(()=>{d(!1)})},[t]),s)return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12 space-y-4",dir:"rtl",children:[(0,r.jsx)(a.o,{size:"lg",color:"primary"}),(0,r.jsx)("p",{className:"text-gray-600",children:"جارى تحميل تفاصيل الإشعار..."})]});if(b)return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12 space-y-4",dir:"rtl",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-red-500"}),(0,r.jsx)("p",{className:"text-red-600 text-center",children:b})]});if(!x)return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12 space-y-4",dir:"rtl",children:[(0,r.jsx)(m.A,{className:"w-16 h-16 text-gray-400"}),(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"لم يتم العثور على تفاصيل الإشعار"})]});let E=v(x.emergency_type);return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6 space-y-6",dir:"rtl",children:[(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-center items-center gap-3",children:[(0,r.jsx)(E,{className:"w-8 h-8 text-gray-600"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"تفاصيل إشعار الطوارئ"})]}),(0,r.jsx)(l.R,{color:y(x.emergency_type),size:"lg",variant:"flat",className:"text-lg px-4 py-2",children:j(x.emergency_type)})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(n.Z,{className:"shadow-md",children:(0,r.jsxs)(c.U,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)(u.A,{className:"w-6 h-6 text-blue-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"معلومات المبلغ"})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"الاسم:"}),(0,r.jsxs)("span",{className:"font-medium",children:[x.user_first_name," ",x.user_last_name]})]})})]})}),(0,r.jsx)(n.Z,{className:"shadow-md",children:(0,r.jsxs)(c.U,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)(h.A,{className:"w-6 h-6 text-green-600"}),(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"معلومات الموقع والوقت"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"الموقع:"}),(0,r.jsx)("span",{className:"font-medium text-left",children:x.location})]}),(0,r.jsx)(i.y,{}),(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"وقت الإبلاغ:"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-left",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 text-gray-500"}),(0,r.jsx)("span",{className:"font-medium",children:N(x.created_at)})]})]})]})]})})]}),(0,r.jsx)(n.Z,{className:"shadow-md",children:(0,r.jsxs)(c.U,{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"وصف الحالة"}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed text-justify bg-gray-50 p-4 rounded-lg",children:x.description})]})}),x.images&&x.images.length>0&&(0,r.jsx)(n.Z,{className:"shadow-md",children:(0,r.jsxs)(c.U,{className:"p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["الصور المرفقة (",x.images.length,")"]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:x.images.map((e,t)=>{let{id:s,src:a}=e;return(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(o.W,{src:a||"/placeholder.svg?height=240&width=240",alt:"صورة ".concat(t+1),width:300,height:200,className:"object-cover rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white opacity-0 group-hover:opacity-100 transition-opacity",children:"انقر للتكبير"})})]},s)})})]})})]})}},59434:(e,t,s)=>{s.d(t,{G:()=>n,cn:()=>l});var r=s(52596),a=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}async function n(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",r=arguments.length>3?arguments[3]:void 0,a={"Content-Type":"application/json"};r&&(a.Authorization="Bearer ".concat(r));let l={method:s,headers:a,next:{revalidate:60}};t&&"GET"!==s&&(l.body=JSON.stringify(t));try{let t="".concat("http://localhost:8000").concat(e);console.log("Fetching: ".concat(t));let s=await fetch(t,l);return s.ok||console.warn("API request failed: ".concat(t," returned status ").concat(s.status)),s}catch(e){throw console.error("API request failed:",e),e}}},73906:(e,t,s)=>{s.d(t,{$m:()=>h,aF:()=>x,cw:()=>u,g6:()=>m});var r=s(95155),a=s(59434),l=s(60760),n=s(36545),c=s(12115);let i=(0,c.createContext)(void 0),o=e=>{let{children:t}=e,[s,a]=(0,c.useState)(!1);return(0,r.jsx)(i.Provider,{value:{open:s,setOpen:a},children:t})},d=()=>{let e=(0,c.useContext)(i);if(!e)throw Error("useModal must be used within a ModalProvider");return e};function x(e){let{children:t}=e;return(0,r.jsx)(o,{children:t})}let m=e=>{let{children:t,className:s}=e,{setOpen:l}=d();return(0,r.jsx)("button",{className:(0,a.cn)("px-4 py-2 rounded-md text-black dark:text-white text-center relative overflow-hidden",s),onClick:()=>l(!0),children:t})},u=e=>{let{children:t,className:s}=e,{open:i}=d();(0,c.useEffect)(()=>{i?document.body.style.overflow="hidden":document.body.style.overflow="auto"},[i]);let o=(0,c.useRef)(null),{setOpen:x}=d();return g(o,()=>x(!1)),(0,r.jsx)(l.N,{children:i&&(0,r.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:"fixed [perspective:800px] [transform-style:preserve-3d] inset-0 h-full w-full  flex items-center justify-center z-[99999]",children:[(0,r.jsx)(p,{}),(0,r.jsxs)(n.P.div,{ref:o,className:(0,a.cn)("min-h-[50%] max-h-[90%] w-[95%] md:max-w-[80%] lg:max-w-[70%] xl:max-w-[60%] bg-white dark:bg-neutral-950 border border-transparent dark:border-neutral-800 md:rounded-2xl relative z-50 flex flex-col overflow-hidden",s),initial:{opacity:0,scale:.5,rotateX:40,y:40},animate:{opacity:1,scale:1,rotateX:0,y:0},exit:{opacity:0,scale:.8,rotateX:10},transition:{type:"spring",stiffness:260,damping:15},children:[(0,r.jsx)(f,{}),t]})]})})},h=e=>{let{children:t,className:s}=e;return(0,r.jsx)("div",{className:(0,a.cn)("flex flex-col flex-1 p-4 md:p-6 overflow-y-auto",s),children:t})},p=e=>{let{className:t}=e;return(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:"fixed inset-0 h-full w-full bg-black bg-opacity-50 z-50 ".concat(t)})},f=()=>{let{setOpen:e}=d();return(0,r.jsx)("button",{onClick:()=>e(!1),className:"absolute top-4 right-4 group",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-black dark:text-white h-4 w-4 group-hover:scale-125 group-hover:rotate-3 transition duration-200",children:[(0,r.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,r.jsx)("path",{d:"M18 6l-12 12"}),(0,r.jsx)("path",{d:"M6 6l12 12"})]})})},g=(e,t)=>{(0,c.useEffect)(()=>{let s=s=>{!(!e.current||e.current.contains(s.target))&&t(s)};return document.addEventListener("mousedown",s),document.addEventListener("touchstart",s),()=>{document.removeEventListener("mousedown",s),document.removeEventListener("touchstart",s)}},[e,t])}}}]);