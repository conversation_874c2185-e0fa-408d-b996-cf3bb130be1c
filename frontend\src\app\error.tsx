"use client"

import { Button } from "@nextui-org/button"
import { useEffect } from "react"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error("Application error:", error)
  }, [error])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <h2 className="text-2xl font-bold mb-4">حدث خطأ غير متوقع</h2>
      <p className="mb-6 text-gray-600">نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.</p>
      <div className="flex gap-4">
        <Button color="primary" onClick={() => reset()}>
          إعادة المحاولة
        </Button>
        <Button variant="bordered" onClick={() => (window.location.href = "/")}>
          العودة للصفحة الرئيسية
        </Button>
      </div>
    </div>
  )
}
