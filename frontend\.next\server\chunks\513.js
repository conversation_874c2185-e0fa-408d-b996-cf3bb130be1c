"use strict";exports.id=513,exports.ids=[513],exports.modules={13353:(t,e,i)=>{i.d(e,{P:()=>tC});var s=i(23671),o=i(82082),r=i(62923),n=i(24325),a=i(59039),l=i(96184),h=i(68028),u=i(14296),d=i(97758),c=i(83361),m=i(24342),p=i(15508),g=i(23294),y=i(80722),v=i(87556);let f=(t,e)=>t.depth-e.depth;class x{constructor(){this.children=[],this.isDirty=!1}add(t){(0,v.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,v.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(f),this.isDirty=!1,this.children.forEach(t)}}var P=i(61866),T=i(32874),D=i(52716),A=i(64068);let E=["TopLeft","TopRight","BottomLeft","BottomRight"],R=E.length,S=t=>"string"==typeof t?parseFloat(t):t,j=t=>"number"==typeof t||T.px.test(t);function k(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let w=B(0,.5,D.yT),L=B(.5,.95,c.l);function B(t,e,i){return s=>s<t?0:s>e?1:i((0,A.q)(t,e,s))}function C(t,e){t.min=e.min,t.max=e.max}function V(t,e){C(t.x,e.x),C(t.y,e.y)}function M(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var U=i(42485),b=i(20172);function F(t,e,i,s,o){return t-=e,t=(0,U.hq)(t,1/i,s),void 0!==o&&(t=(0,U.hq)(t,1/o,s)),t}function O(t,e,[i,s,o],r,n){!function(t,e=0,i=1,s=.5,o,r=t,n=t){if(T.KN.test(e)&&(e=parseFloat(e),e=(0,h.k)(n.min,n.max,e/100)-n.min),"number"!=typeof e)return;let a=(0,h.k)(r.min,r.max,s);t===r&&(a-=e),t.min=F(t.min,e,i,a,o),t.max=F(t.max,e,i,a,o)}(t,e[i],e[s],e[o],e.scale,r,n)}let $=["x","scaleX","originX"],I=["y","scaleY","originY"];function N(t,e,i,s){O(t.x,e,$,i?i.x:void 0,s?s.x:void 0),O(t.y,e,I,i?i.y:void 0,s?s.y:void 0)}var X=i(54538);function G(t){return 0===t.translate&&1===t.scale}function W(t){return G(t.x)&&G(t.y)}function Q(t,e){return t.min===e.min&&t.max===e.max}function H(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function z(t,e){return H(t.x,e.x)&&H(t.y,e.y)}function q(t){return(0,b.CQ)(t.x)/(0,b.CQ)(t.y)}function Y(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class K{constructor(){this.members=[]}add(t){(0,v.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,v.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var Z=i(96633),_=i(27642),J=i(67606),tt=i(25103);let te={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ti=["","X","Y","Z"],ts={visibility:"hidden"},to=0;function tr(t,e,i,s){let{latestValues:o}=e;o[t]&&(i[t]=o[t],e.setStaticValue(t,0),s&&(s[t]=0))}function tn({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:c,resetTransform:v}){return class{constructor(t={},i=e?.()){this.id=to++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,o.Q.value&&(te.nodes=te.calculatedTargetDeltas=te.calculatedProjections=0),this.nodes.forEach(th),this.nodes.forEach(ty),this.nodes.forEach(tv),this.nodes.forEach(tu),o.Q.addProjectionMetrics&&o.Q.addProjectionMetrics(te)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new x)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new u.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:o,layout:a,visualElement:l}=this.options;if(l&&!l.current&&l.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(a||o)&&(this.isLayoutDirty=!0),t){let i;let o=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=n.k.now(),o=({timestamp:r})=>{let n=r-i;n>=250&&((0,s.WG)(o),t(n-e))};return s.Gt.setup(o,!0),()=>(0,s.WG)(o)}(o,250),tt.w.hasAnimatedSinceResize&&(tt.w.hasAnimatedSinceResize=!1,this.nodes.forEach(tg))})}o&&this.root.registerSharedNode(o,this),!1!==this.options.animate&&l&&(o||a)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||l.getDefaultTransition()||tA,{onLayoutAnimationStart:n,onLayoutAnimationComplete:a}=l.getProps(),h=!this.targetLayout||!z(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...(0,r.r)(o,"layout"),onPlay:n,onComplete:a};(l.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||tg(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,s.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(tf),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let o=(0,y.P)(i);if(window.MotionHasOptimisedAnimation(o,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(o,"transform",s.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let o=this.getTransformTemplate();this.prevTransformTemplateValue=o?o(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(tc);return}this.isUpdating||this.nodes.forEach(tm),this.isUpdating=!1,this.nodes.forEach(tp),this.nodes.forEach(ta),this.nodes.forEach(tl),this.clearAllSnapshots();let t=n.k.now();s.uv.delta=(0,d.q)(0,1e3/60,t-s.uv.timestamp),s.uv.timestamp=t,s.uv.isProcessing=!0,s.PP.update.process(s.uv),s.PP.preRender.process(s.uv),s.PP.render.process(s.uv),s.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,a.k.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(td),this.sharedNodes.forEach(tx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,s.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){s.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||(0,b.CQ)(this.snapshot.measuredBox.x)||(0,b.CQ)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,X.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=c(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!v)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!W(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,o=s!==this.prevTransformTemplateValue;t&&(e||(0,J.HD)(this.latestValues)||o)&&(v(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),tS((e=s).x),tS(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,X.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(tk))){let{scroll:t}=this.root;t&&((0,U.Ql)(e.x,t.offset.x),(0,U.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,X.ge)();if(V(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:o,options:r}=s;s!==this.root&&o&&r.layoutScroll&&(o.wasRoot&&V(e,t),(0,U.Ql)(e.x,o.offset.x),(0,U.Ql)(e.y,o.offset.y))}return e}applyTransform(t,e=!1){let i=(0,X.ge)();V(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,U.Ww)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,J.HD)(s.latestValues)&&(0,U.Ww)(i,s.latestValues)}return(0,J.HD)(this.latestValues)&&(0,U.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,X.ge)();V(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,J.HD)(i.latestValues))continue;(0,J.vk)(i.latestValues)&&i.updateSnapshot();let s=(0,X.ge)();V(s,i.measurePageBox()),N(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,J.HD)(this.latestValues)&&N(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==s.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=s.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,X.ge)(),this.relativeTargetOrigin=(0,X.ge)(),(0,b.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),V(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,X.ge)(),this.targetWithTransforms=(0,X.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,b.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):V(this.target,this.layout.layoutBox),(0,U.o4)(this.target,this.targetDelta)):V(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,X.ge)(),this.relativeTargetOrigin=(0,X.ge)(),(0,b.jA)(this.relativeTargetOrigin,this.target,t.target),V(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}o.Q.value&&te.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||(0,J.vk)(this.parent.latestValues)||(0,J.vF)(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===s.uv.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;V(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,l=this.treeScale.y;(0,U.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,X.ge)());let{target:h}=t;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(M(this.prevProjectionDelta.x,this.projectionDelta.x),M(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,b.vb)(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===a&&this.treeScale.y===l&&Y(this.projectionDelta.x,this.prevProjectionDelta.x)&&Y(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),o.Q.value&&te.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,X.xU)(),this.projectionDelta=(0,X.xU)(),this.projectionDeltaWithTransform=(0,X.xU)()}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,o=s?s.latestValues:{},r={...this.latestValues},n=(0,X.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=(0,X.ge)(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,c=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(tD));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(tP(n.x,t.x,s),tP(n.y,t.y,s),this.setTargetDelta(n),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,m,p,g,y,v;if((0,b.jA)(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,g=this.relativeTargetOrigin,y=a,v=s,tT(p.x,g.x,y.x,v),tT(p.y,g.y,y.y,v),i&&(u=this.relativeTarget,m=i,Q(u.x,m.x)&&Q(u.y,m.y)))this.isProjectionDirty=!1;i||(i=(0,X.ge)()),V(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,o,r){o?(t.opacity=(0,h.k)(0,i.opacity??1,w(s)),t.opacityExit=(0,h.k)(e.opacity??1,0,L(s))):r&&(t.opacity=(0,h.k)(e.opacity??1,i.opacity??1,s));for(let o=0;o<R;o++){let r=`border${E[o]}Radius`,n=k(e,r),a=k(i,r);(void 0!==n||void 0!==a)&&(n||(n=0),a||(a=0),0===n||0===a||j(n)===j(a)?(t[r]=Math.max((0,h.k)(S(n),S(a),s),0),(T.KN.test(a)||T.KN.test(n))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,h.k)(e.rotate||0,i.rotate||0,s))}(r,o,this.latestValues,s,c,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,s.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=s.Gt.update(()=>{tt.w.hasAnimatedSinceResize=!0,l.q.layout++,this.currentAnimation=function(t,e,i){let s=(0,p.S)(0)?0:(0,m.OQ)(t);return s.start((0,g.f)("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{l.q.layout--},onComplete:()=>{l.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:o}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&tj(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,X.ge)();let e=(0,b.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=(0,b.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}V(e,i),(0,U.Ww)(e,o),(0,b.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,o)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new K),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&tr("z",t,s,this.animationValues);for(let e=0;e<ti.length;e++)tr(`rotate${ti[e]}`,t,s,this.animationValues),tr(`skew${ti[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ts;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=(0,P.u)(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=(0,P.u)(t?.pointerEvents)||""),this.hasProjected&&!(0,J.HD)(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",o=t.x.translate/e.x,r=t.y.translate/e.y,n=i?.z||0;if((o||r||n)&&(s=`translate3d(${o}px, ${r}px, ${n}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:o,rotateY:r,skewX:n,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),o&&(s+=`rotateX(${o}deg) `),r&&(s+=`rotateY(${r}deg) `),n&&(s+=`skewX(${n}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),i&&(e.transform=i(o,e.transform));let{x:r,y:n}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*n.origin}% 0`,s.animationValues?e.opacity=s===this?o.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:e.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,Z.H){if(void 0===o[t])continue;let{correct:i,applyTo:r,isCSSVariable:n}=Z.H[t],a="none"===e.transform?o[t]:i(o[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=a}else n?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?(0,P.u)(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(tc),this.root.sharedNodes.clear()}}}function ta(t){t.updateLayout()}function tl(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;"size"===o?(0,_.X)(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],o=(0,b.CQ)(s);s.min=i[t].min,s.max=s.min+o}):tj(o,e.layoutBox,i)&&(0,_.X)(s=>{let o=r?e.measuredBox[s]:e.layoutBox[s],n=(0,b.CQ)(i[s]);o.max=o.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+n)});let n=(0,X.xU)();(0,b.vb)(n,i,e.layoutBox);let a=(0,X.xU)();r?(0,b.vb)(a,t.applyTransform(s,!0),e.measuredBox):(0,b.vb)(a,i,e.layoutBox);let l=!W(n),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:o,layout:r}=s;if(o&&r){let n=(0,X.ge)();(0,b.jA)(n,e.layoutBox,o.layoutBox);let a=(0,X.ge)();(0,b.jA)(a,i,r.layoutBox),z(n,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function th(t){o.Q.value&&te.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function tu(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function td(t){t.clearSnapshot()}function tc(t){t.clearMeasurements()}function tm(t){t.isLayoutDirty=!1}function tp(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function tg(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ty(t){t.resolveTargetDelta()}function tv(t){t.calcProjection()}function tf(t){t.resetSkewAndRotation()}function tx(t){t.removeLeadSnapshot()}function tP(t,e,i){t.translate=(0,h.k)(e.translate,0,i),t.scale=(0,h.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function tT(t,e,i,s){t.min=(0,h.k)(e.min,i.min,s),t.max=(0,h.k)(e.max,i.max,s)}function tD(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let tA={duration:.45,ease:[.4,0,.1,1]},tE=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),tR=tE("applewebkit/")&&!tE("chrome/")?Math.round:c.l;function tS(t){t.min=tR(t.min),t.max=tR(t.max)}function tj(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,b.HQ)(q(e),q(i),.2)}function tk(t){return t!==t.root&&t.scroll?.wasRoot}var tw=i(58902);let tL=tn({attachResizeListener:(t,e)=>(0,tw.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tB={current:void 0},tC=tn({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tB.current){let t=new tL({});t.mount(window),t.setOptions({layoutScroll:!0}),tB.current=t}return tB.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},20172:(t,e,i)=>{i.d(e,{CQ:()=>o,HQ:()=>r,N:()=>h,jA:()=>d,vb:()=>a});var s=i(68028);function o(t){return t.max-t.min}function r(t,e,i){return Math.abs(t-e)<=i}function n(t,e,i,r=.5){t.origin=r,t.originPoint=(0,s.k)(e.min,e.max,t.origin),t.scale=o(i)/o(e),t.translate=(0,s.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,s){n(t.x,e.x,i.x,s?s.originX:void 0),n(t.y,e.y,i.y,s?s.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+o(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+o(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},25103:(t,e,i)=>{i.d(e,{w:()=>s});let s={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},27642:(t,e,i)=>{i.d(e,{X:()=>s});function s(t){return[t("x"),t("y")]}},31208:(t,e,i)=>{i.d(e,{Z:()=>r});var s=i(13353),o=i(86652);let r={layout:{ProjectionNode:s.P,MeasureLayout:o.$}}},62688:(t,e,i)=>{i.d(e,{A:()=>l});var s=i(43210);let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim();var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,s.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:a="",children:l,iconNode:h,...u},d)=>(0,s.createElement)("svg",{ref:d,...n,width:e,height:e,stroke:t,strokeWidth:o?24*Number(i)/Number(e):i,className:r("lucide",a),...u},[...h.map(([t,e])=>(0,s.createElement)(t,e)),...Array.isArray(l)?l:[l]])),l=(t,e)=>{let i=(0,s.forwardRef)(({className:i,...n},l)=>(0,s.createElement)(a,{ref:l,iconNode:e,className:r(`lucide-${o(t)}`,i),...n}));return i.displayName=`${t}`,i}},81939:(t,e,i)=>{i.d(e,{$:()=>W});var s=i(64496),o=i(83361),r=i(26181),n=i(32874),a=i(23671),l=i(68028),h=i(66244),u=i(23294),d=i(58902),c=i(27100);function m(t,e,i,s){return(0,d.k)(t,e,(0,c.F)(i),s)}var p=i(32572),g=i(20172),y=i(54538),v=i(27642),f=i(92953);let x=({current:t})=>t?t.ownerDocument.defaultView:null;var P=i(39853),T=i(67283),D=i(28328),A=i(78205),E=i(57211);let R=(t,e)=>Math.abs(t-e);class S{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=w(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(R(t.x,e.x)**2+R(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:o}=a.uv;this.history.push({...s,timestamp:o});let{onStart:r,onMove:n}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),n&&n(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=j(e,this.transformPagePoint),a.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:o}=this.handlers;if(this.dragSnapToOrigin&&o&&o(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=w("pointercancel"===t.type?this.lastMoveEventInfo:j(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!(0,D.M)(t))return;this.dragSnapToOrigin=o,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=j((0,c.e)(t),this.transformPagePoint),{point:n}=r,{timestamp:l}=a.uv;this.history=[{...n,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,w(r,this.history)),this.removeListeners=(0,A.F)(m(this.contextWindow,"pointermove",this.handlePointerMove),m(this.contextWindow,"pointerup",this.handlePointerUp),m(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,a.WG)(this.updatePoint)}}function j(t,e){return e?{point:e(t.point)}:t}function k(t,e){return{x:t.x-e.x,y:t.y-e.y}}function w({point:t},e){return{point:t,delta:k(t,L(e)),offset:k(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,o=L(t);for(;i>=0&&(s=t[i],!(o.timestamp-s.timestamp>(0,E.f)(.1)));)i--;if(!s)return{x:0,y:0};let r=(0,E.X)(o.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let n={x:(o.x-s.x)/r,y:(o.y-s.y)/r};return n.x===1/0&&(n.x=0),n.y===1/0&&(n.y=0),n}(e,.1)}}function L(t){return t[t.length-1]}var B=i(64068),C=i(97758);function V(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function M(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function U(t,e,i){return{min:b(t,e),max:b(t,i)}}function b(t,e){return"number"==typeof t?t:t[e]||0}let F=new WeakMap;class O{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,y.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new S(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,c.e)(t).point)},onStart:(t,e)=>{var i;let{drag:s,dragPropagation:o,onDragStart:l}=this.getProps();if(s&&!o&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=s)||"y"===i?r.I[i]?null:(r.I[i]=!0,()=>{r.I[i]=!1}):r.I.x||r.I.y?null:(r.I.x=r.I.y=!0,()=>{r.I.x=r.I.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,v.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(n.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=(0,g.CQ)(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),l&&a.Gt.postRender(()=>l(t,e)),(0,T.g)(this.visualElement,"transform");let{animationState:h}=this.visualElement;h&&h.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:o,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:n}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(n),null!==this.currentDirection&&o&&o(this.currentDirection);return}this.updateAxis("x",e.point,n),this.updateAxis("y",e.point,n),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,v.X)(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:x(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&a.Gt.postRender(()=>o(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!$(t,s,this.currentDirection))return;let o=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,l.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,l.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),o.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&(0,P.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:o}){return{x:V(t.x,i,o),y:V(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:U(t,"left","right"),y:U(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&(0,v.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!(0,P.X)(e))return!1;let s=e.current;(0,h.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let r=(0,f.L)(s,o.root,this.visualElement.getTransformPagePoint()),n={x:M((t=o.layout.layoutBox).x,r.x),y:M(t.y,r.y)};if(i){let t=i((0,p.pA)(n));this.hasMutatedConstraints=!!t,t&&(n=(0,p.FY)(t))}return n}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:n}=this.getProps(),a=this.constraints||{};return Promise.all((0,v.X)(n=>{if(!$(n,e,this.currentDirection))return;let l=a&&a[n]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[n]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...o,...l};return this.startAxisValueAnimation(n,h)})).then(n)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,T.g)(this.visualElement,t),i.start((0,u.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){(0,v.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,v.X)(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){(0,v.X)(e=>{let{drag:i}=this.getProps();if(!$(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,o=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];o.set(t[e]-(0,l.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!(0,P.X)(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};(0,v.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=(0,g.CQ)(t),o=(0,g.CQ)(e);return o>s?i=(0,B.q)(e.min,e.max-s,t.min):s>o&&(i=(0,B.q)(t.min,t.max-o,e.min)),(0,C.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),(0,v.X)(e=>{if(!$(e,t,null))return;let i=this.getAxisMotionValue(e),{min:o,max:r}=this.constraints[e];i.set((0,l.k)(o,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;F.set(this.visualElement,this);let t=m(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();(0,P.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),a.Gt.read(e);let o=(0,d.k)(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,v.X)(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{o(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:r=.35,dragMomentum:n=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:o,dragElastic:r,dragMomentum:n}}}function $(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class I extends s.X{constructor(t){super(t),this.removeGroupControls=o.l,this.removeListeners=o.l,this.controls=new O(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||o.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let N=t=>(e,i)=>{t&&a.Gt.postRender(()=>t(e,i))};class X extends s.X{constructor(){super(...arguments),this.removePointerDownListener=o.l}onPointerDown(t){this.session=new S(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:x(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:N(t),onStart:N(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&a.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=m(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var G=i(86652);let W={pan:{Feature:X},drag:{Feature:I,ProjectionNode:i(13353).P,MeasureLayout:G.$}}},86652:(t,e,i)=>{i.d(e,{$:()=>f});var s=i(60687),o=i(23671),r=i(59039),n=i(43210),a=i(86044),l=i(12157),h=i(83641),u=i(25103),d=i(32874);function c(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let m={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!d.px.test(t))return t;t=parseFloat(t)}let i=c(t,e.target.x),s=c(t,e.target.y);return`${i}% ${s}%`}};var p=i(39664),g=i(68028),y=i(96633);class v extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:o}=t;(0,y.$)(x),o&&(e.group&&e.group.add(o),i&&i.register&&s&&i.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),u.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,n=i.projection;return n&&(n.isPresent=r,s||t.layoutDependency!==e||void 0===e||t.isPresent!==r?n.willUpdate():this.safeToRemove(),t.isPresent===r||(r?n.promote():n.relegate()||o.Gt.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r.k.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function f(t){let[e,i]=(0,a.xQ)(),o=(0,n.useContext)(l.L);return(0,s.jsx)(v,{...t,layoutGroup:o,switchLayoutGroup:(0,n.useContext)(h.N),isPresent:e,safeToRemove:i})}let x={borderRadius:{...m,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:m,borderTopRightRadius:m,borderBottomLeftRadius:m,borderBottomRightRadius:m,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=p.f.parse(t);if(s.length>5)return t;let o=p.f.createTransformer(t),r=+("number"!=typeof s[0]),n=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=n,s[1+r]/=a;let l=(0,g.k)(n,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),o(s)}}}}};