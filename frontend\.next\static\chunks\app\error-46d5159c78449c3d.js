(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{51901:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var l=s(95155),c=s(66146),n=s(12115);function i(e){let{error:r,reset:s}=e;return(0,n.useEffect)(()=>{console.error("Application error:",r)},[r]),(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen p-6 text-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"حدث خطأ غير متوقع"}),(0,l.jsx)("p",{className:"mb-6 text-gray-600",children:"نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية."}),(0,l.jsxs)("div",{className:"flex gap-4",children:[(0,l.jsx)(c.T,{color:"primary",onClick:()=>s(),children:"إعادة المحاولة"}),(0,l.jsx)(c.T,{variant:"bordered",onClick:()=>window.location.href="/",children:"العودة للصفحة الرئيسية"})]})]})}},58584:(e,r,s)=>{Promise.resolve().then(s.bind(s,51901))}},e=>{var r=r=>e(e.s=r);e.O(0,[146,441,684,358],()=>r(58584)),_N_E=e.O()}]);