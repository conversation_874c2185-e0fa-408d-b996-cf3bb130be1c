import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

const API_URL =
  process.env.NEXT_PUBLIC_BACKEND_URL;

export async function fetcher(
  endpoint: string,
  data: any,
  method: "GET" | "POST" | "PUT" | "DELETE" = "GET",
  token?: string
): Promise<Response> {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  }

  const options: RequestInit = {
    method,
    headers,
    next: { revalidate: 60 }, // Cache for 60 seconds
  };

  if (data && method !== "GET") {
    options.body = JSON.stringify(data);
  }

  try {
    const url = `${API_URL}${endpoint}`;
    console.log(`Fetching: ${url}`);
    const response = await fetch(url, options);

    // Log non-OK responses for debugging
    if (!response.ok) {
      console.warn(
        `API request failed: ${url} returned status ${response.status}`
      );
    }

    return response;
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
}
