"use strict";exports.id=381,exports.ids=[381],exports.modules={31390:(e,t,r)=>{r.d(t,{r:()=>J});var l=r(26109),a=r(54514),n=r(16060),o=r(82432),s=r(25381);let i=new WeakMap;function d(e,t,r){if(!e)return"";"string"==typeof t&&(t=t.replace(/\s+/g,""));let l=i.get(e);return`${l}-${r}-${t}`}var u=r(37313),c=r(97838),b=r(7717),g=r(43210),f=r(6409),p=r(60687),h=(0,l.Rf)((e,t)=>{var r,l;let{as:i,tabKey:h,destroyInactiveTabPanel:m,state:v,className:y,slots:x,classNames:w,...K}=e,C=(0,a.zD)(t),{tabPanelProps:D}=function(e,t,r){var l;let a=!function(e,t){let r,[l,a]=(0,g.useState)(!1);return(0,b.N)(()=>{if((null==e?void 0:e.current)&&!r){let t=()=>{e.current&&a(!!(0,c.N$)(e.current,{tabbable:!0}).nextNode())};t();let r=new MutationObserver(t);return r.observe(e.current,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["tabIndex","disabled"]}),()=>{r.disconnect()}}}),!r&&l}(r)?0:void 0,n=d(t,null!==(l=e.id)&&void 0!==l?l:null==t?void 0:t.selectedKey,"tabpanel"),o=(0,u.b)({...e,id:n,"aria-labelledby":d(t,null==t?void 0:t.selectedKey,"tab")});return{tabPanelProps:(0,s.v)(o,{tabIndex:a,role:"tabpanel","aria-describedby":e["aria-describedby"],"aria-details":e["aria-details"]})}}({...e,id:String(h)},v,C),{focusProps:k,isFocused:L,isFocusVisible:N}=(0,f.o)(),S=v.selectedItem,M=v.collection.getItem(h).props.children,A=(0,n.$)(null==w?void 0:w.panel,y,null==(r=null==S?void 0:S.props)?void 0:r.className),E=h===(null==S?void 0:S.key);return M&&(E||!m)?(0,p.jsx)(i||"div",{ref:C,"data-focus":L,"data-focus-visible":N,"data-inert":E?void 0:"true",inert:(0,o.QA)(!E),...E&&(0,s.v)(D,k,K),className:null==(l=x.panel)?void 0:l.call(x,{class:A}),"data-slot":"panel",children:M}):null});h.displayName="NextUI.TabPanel";var m=r(1172),v=r(73094),y=r(72406),x=r(46474),w=r(45427),K=r(66775),C=r(31294),D=r(40182),k=r(56757),L=r(81939),N=r(31208);let S={...r(37251).l,...L.$,...N.Z};var M=r(70079),A=(0,l.Rf)((e,t)=>{var r;let{className:l,as:o,item:i,state:u,classNames:c,isDisabled:b,listRef:h,slots:L,motionProps:N,disableAnimation:A,disableCursorAnimation:E,shouldSelectOnPressUp:I,onClick:P,tabRef:W,...R}=e,{key:j}=i,$=(0,a.zD)(t),F=o||(e.href?"a":"button"),{tabProps:z,isSelected:H,isDisabled:O,isPressed:T}=function(e,t,r){let{key:l,isDisabled:a,shouldSelectOnPressUp:n}=e,{selectionManager:o,selectedKey:i}=t,u=l===i,c=a||t.isDisabled||t.selectionManager.isDisabled(l),{itemProps:b,isPressed:g}=(0,C.p)({selectionManager:o,key:l,ref:r,isDisabled:c,shouldSelectOnPressUp:n,linkBehavior:"selection"}),f=d(t,l,"tab"),p=d(t,l,"tabpanel"),{tabIndex:h}=b,m=t.collection.getItem(l),v=(0,w.$)(null==m?void 0:m.props,{labelable:!0});delete v.id;let y=(0,K._h)(null==m?void 0:m.props);return{tabProps:(0,s.v)(v,y,b,{id:f,"aria-selected":u,"aria-disabled":c||void 0,"aria-controls":u?p:void 0,tabIndex:c?void 0:h,role:"tab"}),isSelected:u,isDisabled:c,isPressed:g}}({key:j,isDisabled:b,shouldSelectOnPressUp:I},u,$);null==e.children&&delete z["aria-controls"];let B=b||O,{focusProps:V,isFocused:_,isFocusVisible:U}=(0,f.o)(),{hoverProps:Y,isHovered:X}=(0,D.M)({isDisabled:B}),q=(0,n.$)(null==c?void 0:c.tab,l),[,G]=function(e={}){let{rerender:t=!1,delay:r=0}=e,l=(0,g.useRef)(!1),[a,n]=(0,g.useState)(!1);return(0,g.useEffect)(()=>{l.current=!0;let e=null;return t&&(r>0?e=setTimeout(()=>{n(!0)},r):n(!0)),()=>{l.current=!1,t&&n(!1),e&&clearTimeout(e)}},[t]),[(0,g.useCallback)(()=>l.current,[]),a]}({rerender:!0});return(0,p.jsxs)(F,{ref:function(...e){return t=>{e.forEach(e=>(function(e,t){if(null!=e){if((0,m.Tn)(e)){e(t);return}try{e.current=t}catch(r){throw Error(`Cannot assign value '${t}' to ref '${e}'`)}}})(e,t))}}($,W),"data-disabled":(0,m.sE)(O),"data-focus":(0,m.sE)(_),"data-focus-visible":(0,m.sE)(U),"data-hover":(0,m.sE)(X),"data-hover-unselected":(0,m.sE)((X||T)&&!H),"data-pressed":(0,m.sE)(T),"data-selected":(0,m.sE)(H),"data-slot":"tab",...(0,s.v)(z,B?{}:{...V,...Y},(0,v.$)(R,{enabled:"string"==typeof F,omitPropNames:new Set(["title"])}),{onClick:()=>{(0,y.c)(P,z.onClick),(null==$?void 0:$.current)&&(null==h?void 0:h.current)&&(0,x.A)($.current,{scrollMode:"if-needed",behavior:"smooth",block:"end",inline:"end",boundary:null==h?void 0:h.current})}}),className:null==(r=L.tab)?void 0:r.call(L,{class:q}),title:null==R?void 0:R.titleValue,type:"button"===F?"button":void 0,children:[H&&!A&&!E&&G?(0,p.jsx)(k.F,{features:S,children:(0,p.jsx)(M.m.span,{className:L.cursor({class:null==c?void 0:c.cursor}),"data-slot":"cursor",layoutDependency:!1,layoutId:"cursor",transition:{type:"spring",bounce:.15,duration:.5},...N})}):null,(0,p.jsx)("div",{className:L.tabContent({class:null==c?void 0:c.tabContent}),"data-slot":"tabContent",children:i.rendered})]})});A.displayName="NextUI.Tab";var E=r(55150),I=r(85044),P=r(72926),W=r(65146),R=(0,P.tv)({slots:{base:"inline-flex",tabList:["flex","p-1","h-fit","gap-2","items-center","flex-nowrap","overflow-x-scroll","scrollbar-hide","bg-default-100"],tab:["z-0","w-full","px-3","py-1","flex","group","relative","justify-center","items-center","outline-none","cursor-pointer","transition-opacity","tap-highlight-transparent","data-[disabled=true]:cursor-not-allowed","data-[disabled=true]:opacity-30","data-[hover-unselected=true]:opacity-disabled",...W.zb],tabContent:["relative","z-10","text-inherit","whitespace-nowrap","transition-colors","text-default-500","group-data-[selected=true]:text-foreground"],cursor:["absolute","z-0","bg-white"],panel:["py-3","px-1","outline-none","data-[inert=true]:hidden",...W.zb],wrapper:[]},variants:{variant:{solid:{cursor:"inset-0"},light:{tabList:"bg-transparent dark:bg-transparent",cursor:"inset-0"},underlined:{tabList:"bg-transparent dark:bg-transparent",cursor:"h-[2px] w-[80%] bottom-0 shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]"},bordered:{tabList:"bg-transparent dark:bg-transparent border-medium border-default-200 shadow-sm",cursor:"inset-0"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{tabList:"rounded-medium",tab:"h-7 text-tiny rounded-small",cursor:"rounded-small"},md:{tabList:"rounded-medium",tab:"h-8 text-small rounded-small",cursor:"rounded-small"},lg:{tabList:"rounded-large",tab:"h-9 text-medium rounded-medium",cursor:"rounded-medium"}},radius:{none:{tabList:"rounded-none",tab:"rounded-none",cursor:"rounded-none"},sm:{tabList:"rounded-medium",tab:"rounded-small",cursor:"rounded-small"},md:{tabList:"rounded-medium",tab:"rounded-small",cursor:"rounded-small"},lg:{tabList:"rounded-large",tab:"rounded-medium",cursor:"rounded-medium"},full:{tabList:"rounded-full",tab:"rounded-full",cursor:"rounded-full"}},fullWidth:{true:{base:"w-full",tabList:"w-full"}},isDisabled:{true:{tabList:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{tab:"transition-none",tabContent:"transition-none"}},placement:{top:{},start:{tabList:"flex-col",panel:"py-0 px-3",wrapper:"flex"},end:{tabList:"flex-col",panel:"py-0 px-3",wrapper:"flex flex-row-reverse"},bottom:{wrapper:"flex flex-col-reverse"}}},defaultVariants:{color:"default",variant:"solid",size:"md",fullWidth:!1,isDisabled:!1},compoundVariants:[{variant:["solid","bordered","light"],color:"default",class:{cursor:["bg-background","dark:bg-default","shadow-small"],tabContent:"group-data-[selected=true]:text-default-foreground"}},{variant:["solid","bordered","light"],color:"primary",class:{cursor:I.k.solid.primary,tabContent:"group-data-[selected=true]:text-primary-foreground"}},{variant:["solid","bordered","light"],color:"secondary",class:{cursor:I.k.solid.secondary,tabContent:"group-data-[selected=true]:text-secondary-foreground"}},{variant:["solid","bordered","light"],color:"success",class:{cursor:I.k.solid.success,tabContent:"group-data-[selected=true]:text-success-foreground"}},{variant:["solid","bordered","light"],color:"warning",class:{cursor:I.k.solid.warning,tabContent:"group-data-[selected=true]:text-warning-foreground"}},{variant:["solid","bordered","light"],color:"danger",class:{cursor:I.k.solid.danger,tabContent:"group-data-[selected=true]:text-danger-foreground"}},{variant:"underlined",color:"default",class:{cursor:"bg-foreground",tabContent:"group-data-[selected=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{cursor:"bg-primary",tabContent:"group-data-[selected=true]:text-primary"}},{variant:"underlined",color:"secondary",class:{cursor:"bg-secondary",tabContent:"group-data-[selected=true]:text-secondary"}},{variant:"underlined",color:"success",class:{cursor:"bg-success",tabContent:"group-data-[selected=true]:text-success"}},{variant:"underlined",color:"warning",class:{cursor:"bg-warning",tabContent:"group-data-[selected=true]:text-warning"}},{variant:"underlined",color:"danger",class:{cursor:"bg-danger",tabContent:"group-data-[selected=true]:text-danger"}},{disableAnimation:!0,variant:"underlined",class:{tab:["after:content-['']","after:absolute","after:bottom-0","after:h-[2px]","after:w-[80%]","after:opacity-0","after:shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","data-[selected=true]:after:opacity-100"]}},{disableAnimation:!0,color:"default",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-default data-[selected=true]:text-default-foreground"}},{disableAnimation:!0,color:"primary",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-primary data-[selected=true]:text-primary-foreground"}},{disableAnimation:!0,color:"secondary",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-secondary data-[selected=true]:text-secondary-foreground"}},{disableAnimation:!0,color:"success",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-success data-[selected=true]:text-success-foreground"}},{disableAnimation:!0,color:"warning",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-warning data-[selected=true]:text-warning-foreground"}},{disableAnimation:!0,color:"danger",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-danger data-[selected=true]:text-danger-foreground"}},{disableAnimation:!0,color:"default",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-foreground"}},{disableAnimation:!0,color:"primary",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-primary"}},{disableAnimation:!0,color:"secondary",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-secondary"}},{disableAnimation:!0,color:"success",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-success"}},{disableAnimation:!0,color:"warning",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-warning"}},{disableAnimation:!0,color:"danger",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-danger"}}],compoundSlots:[{variant:"underlined",slots:["tab","tabList","cursor"],class:["rounded-none"]}]}),j=r(73469),$=r(32168);function F(e,t){let r=null;if(e){var l,a,n,o;for(r=e.getFirstKey();null!=r&&(t.has(r)||(null===(a=e.getItem(r))||void 0===a?void 0:null===(l=a.props)||void 0===l?void 0:l.isDisabled))&&r!==e.getLastKey();)r=e.getKeyAfter(r);null!=r&&(t.has(r)||(null===(o=e.getItem(r))||void 0===o?void 0:null===(n=o.props)||void 0===n?void 0:n.isDisabled))&&r===e.getLastKey()&&(r=e.getFirstKey())}return r}class z{getKeyLeftOf(e){return this.flipDirection?this.getNextKey(e):this.getPreviousKey(e)}getKeyRightOf(e){return this.flipDirection?this.getPreviousKey(e):this.getNextKey(e)}isDisabled(e){var t,r;return this.disabledKeys.has(e)||!!(null===(r=this.collection.getItem(e))||void 0===r?void 0:null===(t=r.props)||void 0===t?void 0:t.isDisabled)}getFirstKey(){let e=this.collection.getFirstKey();return null!=e&&this.isDisabled(e)&&(e=this.getNextKey(e)),e}getLastKey(){let e=this.collection.getLastKey();return null!=e&&this.isDisabled(e)&&(e=this.getPreviousKey(e)),e}getKeyAbove(e){return this.tabDirection?null:this.getPreviousKey(e)}getKeyBelow(e){return this.tabDirection?null:this.getNextKey(e)}getNextKey(e){do null==(e=this.collection.getKeyAfter(e))&&(e=this.collection.getFirstKey());while(this.isDisabled(e));return e}getPreviousKey(e){do null==(e=this.collection.getKeyBefore(e))&&(e=this.collection.getLastKey());while(this.isDisabled(e));return e}constructor(e,t,r,l=new Set){this.collection=e,this.flipDirection="rtl"===t&&"horizontal"===r,this.disabledKeys=l,this.tabDirection="horizontal"===r}}var H=r(58463),O=r(30900),T=r(92e3),B=r(12157);let V=(0,g.createContext)(null);var _=r(23671),U=r(15124);let Y=e=>!e.isLayoutDirty&&e.willUpdate(!1),X=e=>!0===e,q=e=>X(!0===e)||"id"===e,G=({children:e,id:t,inherit:r=!0})=>{let l=(0,g.useContext)(B.L),a=(0,g.useContext)(V),[n,o]=function(){let e=function(){let e=(0,g.useRef)(!1);return(0,U.E)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}(),[t,r]=(0,g.useState)(0),l=(0,g.useCallback)(()=>{e.current&&r(t+1)},[t]);return[(0,g.useCallback)(()=>_.Gt.postRender(l),[l]),t]}(),s=(0,g.useRef)(null),i=l.id||a;null===s.current&&(q(r)&&i&&(t=t?i+"-"+t:i),s.current={id:t,group:X(r)&&l.group||function(){let e=new Set,t=new WeakMap,r=()=>e.forEach(Y);return{add:l=>{e.add(l),t.set(l,l.addEventListener("willUpdate",r))},remove:l=>{e.delete(l);let a=t.get(l);a&&(a(),t.delete(l)),r()},dirty:r}}()});let d=(0,g.useMemo)(()=>({...s.current,forceRender:n}),[o]);return(0,p.jsx)(B.L.Provider,{value:d,children:e})};var J=(0,l.Rf)(function(e,t){let{Component:r,values:d,state:c,destroyInactiveTabPanel:b,getBaseProps:f,getTabListProps:m,getWrapperProps:y}=function(e){var t,r,d;let c=(0,E.o)(),[b,f]=(0,l.rE)(e,R.variantKeys),{ref:p,as:h,className:m,classNames:y,children:x,disableCursorAnimation:w,motionProps:K,isVertical:C=!1,shouldSelectOnPressUp:D=!0,destroyInactiveTabPanel:k=!0,...L}=b,N=h||"div",S="string"==typeof N,M=(0,a.zD)(p),A=null!=(r=null!=(t=null==e?void 0:e.disableAnimation)?t:null==c?void 0:c.disableAnimation)&&r,I=function(e){var t,r;let l=function(e){var t;let[r,l]=(0,$.P)(e.selectedKey,null!==(t=e.defaultSelectedKey)&&void 0!==t?t:null,e.onSelectionChange),a=(0,g.useMemo)(()=>null!=r?[r]:[],[r]),{collection:n,disabledKeys:o,selectionManager:s}=(0,j.p)({...e,selectionMode:"single",disallowEmptySelection:!0,allowDuplicateSelectionEvents:!0,selectedKeys:a,onSelectionChange:t=>{var a;if("all"===t)return;let n=null!==(a=t.values().next().value)&&void 0!==a?a:null;n===r&&e.onSelectionChange&&e.onSelectionChange(n),l(n)}}),i=null!=r?n.getItem(r):null;return{collection:n,disabledKeys:o,selectionManager:s,selectedKey:r,setSelectedKey:l,selectedItem:i}}({...e,suppressTextValueWarning:!0,defaultSelectedKey:null!==(r=null!==(t=e.defaultSelectedKey)&&void 0!==t?t:F(e.collection,e.disabledKeys?new Set(e.disabledKeys):new Set))&&void 0!==r?r:void 0}),{selectionManager:a,collection:n,selectedKey:o}=l,s=(0,g.useRef)(o);return(0,g.useEffect)(()=>{let e=o;(a.isEmpty||null==e||!n.getItem(e))&&null!=(e=F(n,l.disabledKeys))&&a.setSelectedKeys([e]),(null==e||null!=a.focusedKey)&&(a.isFocused||e===s.current)||a.setFocusedKey(e),s.current=e}),{...l,isDisabled:e.isDisabled||!1}}({children:x,...L}),{tabListProps:P}=function(e,t,r){let{orientation:l="horizontal",keyboardActivation:a="automatic"}=e,{collection:n,selectionManager:o,disabledKeys:d}=t,{direction:c}=(0,O.Y)(),b=(0,g.useMemo)(()=>new z(n,c,l,d),[n,d,l,c]),{collectionProps:f}=(0,T.y)({ref:r,selectionManager:o,keyboardDelegate:b,selectOnFocus:"automatic"===a,disallowEmptySelection:!0,scrollRef:r,linkBehavior:"selection"}),p=(0,H.Bi)();i.set(t,p);let h=(0,u.b)({...e,id:p});return{tabListProps:{...(0,s.v)(f,h),role:"tablist","aria-orientation":l,tabIndex:void 0}}}(L,I,M),W=(0,g.useMemo)(()=>R({...f,className:m,disableAnimation:A,...C?{placement:"start"}:{}}),[(0,o.t6)(f),m,A,C]),B=(0,n.$)(null==y?void 0:y.base,m),V=(0,g.useMemo)(()=>({state:I,slots:W,classNames:y,motionProps:K,disableAnimation:A,listRef:M,shouldSelectOnPressUp:D,disableCursorAnimation:w,isDisabled:null==e?void 0:e.isDisabled}),[I,W,M,K,A,w,D,null==e?void 0:e.isDisabled,y]),_=(0,g.useCallback)(e=>({"data-slot":"base",className:W.base({class:(0,n.$)(B,null==e?void 0:e.className)}),...(0,s.v)((0,v.$)(L,{enabled:S}),e)}),[B,L,W]),U=null!=(d=f.placement)?d:C?"start":"top",Y=(0,g.useCallback)(e=>({"data-slot":"tabWrapper",className:W.wrapper({class:(0,n.$)(null==y?void 0:y.wrapper,null==e?void 0:e.className)}),"data-placement":U,"data-vertical":C||"start"===U||"end"===U?"vertical":"horizontal"}),[y,W,U,C]),X=(0,g.useCallback)(e=>({ref:M,"data-slot":"tabList",className:W.tabList({class:(0,n.$)(null==y?void 0:y.tabList,null==e?void 0:e.className)}),...(0,s.v)(P,e)}),[M,P,y,W]);return{Component:N,domRef:M,state:I,values:V,destroyInactiveTabPanel:k,getBaseProps:_,getTabListProps:X,getWrapperProps:Y}}({...e,ref:t}),x=(0,g.useId)(),w=!e.disableAnimation&&!e.disableCursorAnimation,K={state:c,listRef:d.listRef,slots:d.slots,classNames:d.classNames,isDisabled:d.isDisabled,motionProps:d.motionProps,disableAnimation:d.disableAnimation,shouldSelectOnPressUp:d.shouldSelectOnPressUp,disableCursorAnimation:d.disableCursorAnimation},C=[...c.collection].map(e=>(0,p.jsx)(A,{item:e,...K,...e.props},e.key)),D=(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("div",{...f(),children:(0,p.jsx)(r,{...m(),children:w?(0,p.jsx)(G,{id:x,children:C}):C})}),[...c.collection].map(e=>(0,p.jsx)(h,{classNames:d.classNames,destroyInactiveTabPanel:b,slots:d.slots,state:d.state,tabKey:e.key},e.key))]});return"placement"in e||"isVertical"in e?(0,p.jsx)("div",{...y(),children:D}):D})},37251:(e,t,r)=>{r.d(t,{l:()=>n});var l=r(28310),a=r(58875);let n={renderer:r(82319).J,...l.W,...a.n}},46474:(e,t,r)=>{r.d(t,{A:()=>u});let l=e=>"object"==typeof e&&null!=e&&1===e.nodeType,a=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,n=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let r=getComputedStyle(e,null);return a(r.overflowY,t)||a(r.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},o=(e,t,r,l,a,n,o,s)=>n<e&&o>t||n>e&&o<t?0:n<=e&&s<=r||o>=t&&s>=r?n-e-l:o>t&&s<r||n<e&&s>r?o-t+a:0,s=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},i=(e,t)=>{var r,a,i,d;if("undefined"==typeof document)return[];let{scrollMode:u,block:c,inline:b,boundary:g,skipOverflowHiddenElements:f}=t,p="function"==typeof g?g:e=>e!==g;if(!l(e))throw TypeError("Invalid target");let h=document.scrollingElement||document.documentElement,m=[],v=e;for(;l(v)&&p(v);){if((v=s(v))===h){m.push(v);break}null!=v&&v===document.body&&n(v)&&!n(document.documentElement)||null!=v&&n(v,f)&&m.push(v)}let y=null!=(a=null==(r=window.visualViewport)?void 0:r.width)?a:innerWidth,x=null!=(d=null==(i=window.visualViewport)?void 0:i.height)?d:innerHeight,{scrollX:w,scrollY:K}=window,{height:C,width:D,top:k,right:L,bottom:N,left:S}=e.getBoundingClientRect(),{top:M,right:A,bottom:E,left:I}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),P="start"===c||"nearest"===c?k-M:"end"===c?N+E:k+C/2-M+E,W="center"===b?S+D/2-I+A:"end"===b?L+A:S-I,R=[];for(let e=0;e<m.length;e++){let t=m[e],{height:r,width:l,top:a,right:s,bottom:i,left:d}=t.getBoundingClientRect();if("if-needed"===u&&k>=0&&S>=0&&N<=x&&L<=y&&(t===h&&!n(t)||k>=a&&N<=i&&S>=d&&L<=s))break;let g=getComputedStyle(t),f=parseInt(g.borderLeftWidth,10),p=parseInt(g.borderTopWidth,10),v=parseInt(g.borderRightWidth,10),M=parseInt(g.borderBottomWidth,10),A=0,E=0,I="offsetWidth"in t?t.offsetWidth-t.clientWidth-f-v:0,j="offsetHeight"in t?t.offsetHeight-t.clientHeight-p-M:0,$="offsetWidth"in t?0===t.offsetWidth?0:l/t.offsetWidth:0,F="offsetHeight"in t?0===t.offsetHeight?0:r/t.offsetHeight:0;if(h===t)A="start"===c?P:"end"===c?P-x:"nearest"===c?o(K,K+x,x,p,M,K+P,K+P+C,C):P-x/2,E="start"===b?W:"center"===b?W-y/2:"end"===b?W-y:o(w,w+y,y,f,v,w+W,w+W+D,D),A=Math.max(0,A+K),E=Math.max(0,E+w);else{A="start"===c?P-a-p:"end"===c?P-i+M+j:"nearest"===c?o(a,i,r,p,M+j,P,P+C,C):P-(a+r/2)+j/2,E="start"===b?W-d-f:"center"===b?W-(d+l/2)+I/2:"end"===b?W-s+v+I:o(d,s,l,f,v+I,W,W+D,D);let{scrollLeft:e,scrollTop:n}=t;A=0===F?0:Math.max(0,Math.min(n+A/F,t.scrollHeight-r/F+j)),E=0===$?0:Math.max(0,Math.min(e+E/$,t.scrollWidth-l/$+I)),P+=n-A,W+=e-E}R.push({el:t,top:A,left:E})}return R},d=e=>!1===e?{block:"end",inline:"nearest"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:"start",inline:"nearest"};function u(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(i(e,t));let r="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:l,top:a,left:n}of i(e,d(t)))l.scroll({top:a,left:n,behavior:r})}},71496:(e,t,r)=>{r.d(t,{i:()=>l});var l=r(11223).q}};