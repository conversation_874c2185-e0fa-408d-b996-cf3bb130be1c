import { EmergencyForm } from "@/components/specific/EmergencyForm";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export default async function AddApplicationPage() {
  // Get the cookie on the server
  const cookiesStore = await cookies();
  const access = cookiesStore.get("access")?.value ?? "";

  // If no access token, redirect to login
  if (!access) {
    redirect("/auth?error=not-logged-in");
  }

  return (
    <main className="min-h-screen bg-gray-50">
      <EmergencyForm token={access} />
    </main>
  );
}
