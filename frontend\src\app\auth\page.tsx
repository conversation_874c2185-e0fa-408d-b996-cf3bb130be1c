"use client"
import LoginForm from "@/components/authentication/LoginForm"
import SignUpForm from "@/components/authentication/SignUpForm"
import { fetcher } from "@/lib/utils"
import { Button } from "@nextui-org/button"
import { Tab, Tabs } from "@nextui-org/tabs"
import Image from "next/image"
import { useRouter } from "next/navigation"

type Props = {}

export default function page({}: Props) {
  const router = useRouter()

  const signInGoogle = async () => {
    console.log("Google sign-in initiated")
    try {
      const res = await fetcher("/auth/google/url/", null, "GET")

      if (res.ok) {
        const data = await res.json()
        const url = data.url
        window.location.href = url // Use window.location.href for external redirect
      } else {
        console.error("Failed to get Google auth URL")
      }
    } catch (error) {
      console.error("Error initiating Google sign-in:", error)
    }
  }

  return (
    <>
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold">مرحباً بعودتك!</h1>
          <p className="text-gray-500 mt-2">مرحباً بعودتك، من فضلك ادخل بياناتك.</p>
        </div>
        <Tabs aria-label="Auth options" color="primary" variant="underlined" className="w-full">
          <Tab key="login" title="تسجيل الدخول">
            <LoginForm />
          </Tab>
          <Tab key="signup" title="انشاء حساب ">
            <SignUpForm />
          </Tab>
        </Tabs>
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-gray-500">او اتصل باستخدام</span>
          </div>
        </div>
        <div className="flex justify-center">
          <Button
            variant="ghost"
            onPress={signInGoogle}
            className="border-1 border-gray-200 flex items-center gap-2 px-6 py-2"
          >
            <Image src="/google-icon.png" alt="Google" width={20} height={20} />
            <span>تسجيل الدخول بواسطة Google</span>
          </Button>
        </div>
      </div>
    </>
  )
}
