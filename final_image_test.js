// Final Comprehensive Image Integration Test
// Tests all aspects of image upload, storage, and display

const API_BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

async function finalImageTest() {
    console.log('🎯 Final Comprehensive Image Integration Test');
    console.log('==============================================\n');
    
    const testResults = {
        mediaServing: false,
        apiUrls: false,
        listImages: false,
        detailImages: false,
        uploadEndpoint: false,
        frontendPages: false,
        imageFormat: false,
        cors: false
    };
    
    // Test 1: Media File Serving
    console.log('1. 📁 Testing Media File Serving...');
    try {
        const response = await fetch(`${API_BASE_URL}/media/emergency/images/pngtree-demolished-house-window-tear-down-house-demolition-photo-image_15122860.jpg`);
        
        if (response.ok) {
            const contentType = response.headers.get('content-type');
            console.log('✅ Media files served correctly');
            console.log(`   Content-Type: ${contentType}`);
            console.log(`   Status: ${response.status}`);
            testResults.mediaServing = true;
        } else {
            console.log(`❌ Media serving failed: ${response.status}`);
        }
    } catch (error) {
        console.log(`❌ Media serving error: ${error.message}`);
    }
    
    // Test 2: API Image URLs
    console.log('\n2. 🔗 Testing API Image URLs...');
    try {
        const response = await fetch(`${API_BASE_URL}/emergency/`);
        const data = await response.json();
        
        if (response.ok && data.results && data.results.length > 0) {
            const emergency = data.results[0];
            
            if (emergency.image && emergency.image.startsWith('http://localhost:8000')) {
                console.log('✅ List API returns full URLs');
                console.log(`   Sample: ${emergency.image.substring(0, 50)}...`);
                testResults.apiUrls = true;
                testResults.listImages = true;
            } else {
                console.log('❌ List API URLs incorrect');
                console.log(`   Received: ${emergency.image}`);
            }
        }
    } catch (error) {
        console.log(`❌ API URL test error: ${error.message}`);
    }
    
    // Test 3: Detail View Images
    console.log('\n3. 🖼️  Testing Detail View Images...');
    try {
        const listResponse = await fetch(`${API_BASE_URL}/emergency/`);
        const listData = await listResponse.json();
        
        if (listData.results && listData.results.length > 0) {
            const emergencyId = listData.results[0].id;
            const detailResponse = await fetch(`${API_BASE_URL}/emergency/${emergencyId}/`);
            const detailData = await detailResponse.json();
            
            if (detailResponse.ok && detailData.images) {
                if (detailData.images.length > 0) {
                    const image = detailData.images[0];
                    
                    if (image.id && image.src && image.src.startsWith('http://localhost:8000')) {
                        console.log('✅ Detail view images format correct');
                        console.log(`   Image ID: ${image.id}`);
                        console.log(`   Image URL: ${image.src.substring(0, 50)}...`);
                        testResults.detailImages = true;
                        testResults.imageFormat = true;
                    } else {
                        console.log('❌ Detail view image format incorrect');
                        console.log(`   Image object: ${JSON.stringify(image)}`);
                    }
                } else {
                    console.log('⚠️  No images in detail view to test');
                    testResults.detailImages = true; // Not a failure
                }
            }
        }
    } catch (error) {
        console.log(`❌ Detail view test error: ${error.message}`);
    }
    
    // Test 4: Upload Endpoint
    console.log('\n4. 📤 Testing Upload Endpoint...');
    try {
        const response = await fetch(`${API_BASE_URL}/emergency/create/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
        
        if (response.status === 401 || response.status === 400) {
            console.log('✅ Upload endpoint accessible');
            console.log(`   Status: ${response.status} (expected for unauthenticated request)`);
            testResults.uploadEndpoint = true;
        } else {
            console.log(`⚠️  Upload endpoint status: ${response.status}`);
        }
    } catch (error) {
        console.log(`❌ Upload endpoint error: ${error.message}`);
    }
    
    // Test 5: Frontend Pages
    console.log('\n5. 🌐 Testing Frontend Pages...');
    try {
        const homeResponse = await fetch(`${FRONTEND_URL}/`);
        const formResponse = await fetch(`${FRONTEND_URL}/add-application`);
        
        if (homeResponse.ok && formResponse.ok) {
            console.log('✅ Frontend pages accessible');
            console.log(`   Home: ${homeResponse.status}`);
            console.log(`   Form: ${formResponse.status}`);
            testResults.frontendPages = true;
        } else {
            console.log('❌ Frontend pages not accessible');
        }
    } catch (error) {
        console.log(`❌ Frontend test error: ${error.message}`);
    }
    
    // Test 6: CORS Configuration
    console.log('\n6. 🔒 Testing CORS Configuration...');
    try {
        const response = await fetch(`${API_BASE_URL}/emergency/`, {
            method: 'GET',
            headers: {
                'Origin': 'http://localhost:3000'
            }
        });
        
        const corsHeader = response.headers.get('Access-Control-Allow-Origin');
        if (response.ok && (corsHeader === '*' || corsHeader === 'http://localhost:3000')) {
            console.log('✅ CORS configured correctly');
            console.log(`   CORS Header: ${corsHeader}`);
            testResults.cors = true;
        } else {
            console.log('⚠️  CORS may need configuration');
            console.log(`   CORS Header: ${corsHeader}`);
        }
    } catch (error) {
        console.log(`❌ CORS test error: ${error.message}`);
    }
    
    // Results Summary
    console.log('\n📊 Final Test Results:');
    console.log('======================');
    
    const totalTests = Object.keys(testResults).length;
    const passedTests = Object.values(testResults).filter(Boolean).length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    Object.entries(testResults).forEach(([test, passed]) => {
        const status = passed ? '✅' : '❌';
        const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        console.log(`${status} ${testName}`);
    });
    
    console.log(`\n📈 Overall Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
    
    // Final Assessment
    if (passedTests >= totalTests * 0.8) {
        console.log('\n🎉 IMAGE INTEGRATION SUCCESSFUL!');
        console.log('================================');
        console.log('✅ Backend: Image serving and API endpoints working');
        console.log('✅ Frontend: Pages accessible and ready for image display');
        console.log('✅ Integration: Image URLs and formats correct');
        
        console.log('\n🚀 Ready for Production Use:');
        console.log('============================');
        console.log('• Users can upload images in emergency forms');
        console.log('• Images display correctly in home page cards');
        console.log('• Detail views show all uploaded images');
        console.log('• Full URLs ensure cross-origin compatibility');
        
    } else {
        console.log('\n⚠️  INTEGRATION NEEDS ATTENTION');
        console.log('===============================');
        
        Object.entries(testResults).forEach(([test, passed]) => {
            if (!passed) {
                console.log(`❌ Fix required: ${test}`);
            }
        });
    }
    
    console.log('\n🧪 Manual Verification Steps:');
    console.log('=============================');
    console.log('1. Open: http://localhost:3000/add-application');
    console.log('2. Fill form and upload 1-3 images');
    console.log('3. Submit form');
    console.log('4. Go to: http://localhost:3000/');
    console.log('5. Verify images appear in emergency cards');
    console.log('6. Click "عرض التفاصيل" on any card');
    console.log('7. Verify all images display in detail modal');
    
    return testResults;
}

// Execute the final test
finalImageTest().catch(console.error);
