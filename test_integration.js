// Frontend-Backend Integration Test Script
// This script tests the key integration points between frontend and backend

const API_BASE_URL = 'http://localhost:8000';

async function testAPIEndpoint(endpoint, method = 'GET', data = null, token = null) {
    const headers = {
        'Content-Type': 'application/json',
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    const options = {
        method,
        headers,
    };
    
    if (data && method !== 'GET') {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
        const result = {
            status: response.status,
            ok: response.ok,
            endpoint: endpoint,
            method: method
        };
        
        try {
            result.data = await response.json();
        } catch (e) {
            result.data = await response.text();
        }
        
        return result;
    } catch (error) {
        return {
            status: 'ERROR',
            ok: false,
            endpoint: endpoint,
            method: method,
            error: error.message
        };
    }
}

async function runIntegrationTests() {
    console.log('🚀 Starting Frontend-Backend Integration Tests...\n');
    
    const tests = [
        // Test 1: API Documentation
        {
            name: 'API Documentation',
            test: () => testAPIEndpoint('/api/docs/')
        },
        
        // Test 2: Emergency List (Public)
        {
            name: 'Emergency List (Public)',
            test: () => testAPIEndpoint('/emergency/')
        },
        
        // Test 3: Emergency List with Filter
        {
            name: 'Emergency List with Filter',
            test: () => testAPIEndpoint('/emergency/?emergency_type=M')
        },
        
        // Test 4: JWT Verify (Invalid Token)
        {
            name: 'JWT Verify (Invalid Token)',
            test: () => testAPIEndpoint('/auth/jwt/verify/', 'POST', { token: 'invalid_token' })
        },
        
        // Test 5: Emergency Create (No Auth)
        {
            name: 'Emergency Create (No Auth)',
            test: () => testAPIEndpoint('/emergency/create/', 'POST', {})
        },
        
        // Test 6: User Registration Endpoint
        {
            name: 'User Registration Endpoint',
            test: () => testAPIEndpoint('/users/', 'POST', {
                email: '<EMAIL>',
                password: 'short'  // This should fail validation
            })
        },
        
        // Test 7: API Schema
        {
            name: 'API Schema',
            test: () => testAPIEndpoint('/api/schema/')
        }
    ];
    
    const results = [];
    
    for (const test of tests) {
        console.log(`Testing: ${test.name}...`);
        const result = await test.test();
        results.push({ name: test.name, ...result });
        
        if (result.ok) {
            console.log(`✅ ${test.name} - Status: ${result.status}`);
        } else {
            console.log(`❌ ${test.name} - Status: ${result.status}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        }
        console.log('');
    }
    
    // Summary
    console.log('📊 Test Summary:');
    console.log('================');
    const passed = results.filter(r => r.ok).length;
    const failed = results.filter(r => !r.ok).length;
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / results.length) * 100).toFixed(1)}%`);
    
    // Expected failures (these are actually correct behavior)
    const expectedFailures = [
        'JWT Verify (Invalid Token)',
        'Emergency Create (No Auth)',
        'User Registration Endpoint'
    ];
    
    console.log('\n🔍 Analysis:');
    console.log('=============');
    results.forEach(result => {
        if (!result.ok && expectedFailures.includes(result.name)) {
            console.log(`✅ ${result.name}: Expected failure (correct security behavior)`);
        } else if (!result.ok) {
            console.log(`⚠️  ${result.name}: Unexpected failure - needs investigation`);
        } else {
            console.log(`✅ ${result.name}: Working correctly`);
        }
    });
    
    return results;
}

// Run the tests
runIntegrationTests().catch(console.error);
