/**
 * @jest-environment jsdom
 */
import { render, screen, waitFor } from '@testing-library/react';
import { NextUIProvider } from '@nextui-org/react';
import PreviousPage from '../page';

// Mock the fetcher utility
jest.mock('@/lib/utils', () => ({
  fetcher: jest.fn(),
}));

// Mock the animated modal components
jest.mock('@/components/ui/animated-modal', () => ({
  Modal: ({ children }: { children: React.ReactNode }) => <div data-testid="modal">{children}</div>,
  ModalBody: ({ children }: { children: React.ReactNode }) => <div data-testid="modal-body">{children}</div>,
  ModalTrigger: ({ children }: { children: React.ReactNode }) => <div data-testid="modal-trigger">{children}</div>,
}));

// Mock the AlertItemDetails component
jest.mock('@/components/specific/AlertItemDetails/page', () => ({
  AlertItemDetails: ({ id }: { id: string }) => <div data-testid="alert-details">Alert Details for {id}</div>,
}));

const mockFetcher = require('@/lib/utils').fetcher;

const mockEmergencyData = {
  count: 2,
  next: null,
  previous: null,
  results: [
    {
      id: 1,
      emergency_type: 'M',
      description: 'Medical emergency test',
      location: 'Test Location 1',
      created_at: '2024-01-01T10:00:00Z',
      user_first_name: 'John',
      user_last_name: 'Doe',
      images: [],
    },
    {
      id: 2,
      emergency_type: 'D',
      description: 'Danger alert test',
      location: 'Test Location 2',
      created_at: '2024-01-02T11:00:00Z',
      user_first_name: 'Jane',
      user_last_name: 'Smith',
      images: [],
    },
  ],
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <NextUIProvider>
      {component}
    </NextUIProvider>
  );
};

describe('PreviousPage', () => {
  beforeEach(() => {
    mockFetcher.mockClear();
  });

  it('renders loading state initially', () => {
    mockFetcher.mockImplementation(() => new Promise(() => {})); // Never resolves

    renderWithProviders(<PreviousPage />);

    expect(screen.getByText('جارٍ تحميل الإشعارات...')).toBeInTheDocument();
  });

  it('renders emergency notifications successfully', async () => {
    mockFetcher.mockResolvedValue({
      ok: true,
      text: () => Promise.resolve(JSON.stringify(mockEmergencyData)),
    });

    renderWithProviders(<PreviousPage />);

    await waitFor(() => {
      expect(screen.getByText('الإشعارات السابقة')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(screen.getByText('Medical emergency test')).toBeInTheDocument();
      expect(screen.getByText('Danger alert test')).toBeInTheDocument();
    });

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Test Location 1')).toBeInTheDocument();
    expect(screen.getByText('Test Location 2')).toBeInTheDocument();
  });

  it('renders error state when API fails', async () => {
    mockFetcher.mockRejectedValue(new Error('API Error'));

    renderWithProviders(<PreviousPage />);

    await waitFor(() => {
      expect(screen.getByText('حدث خطأ أثناء تحميل الإشعارات. يرجى المحاولة مرة أخرى.')).toBeInTheDocument();
    });
  });

  it('renders empty state when no notifications', async () => {
    mockFetcher.mockResolvedValue({
      ok: true,
      text: () => Promise.resolve(JSON.stringify({ count: 0, results: [], next: null, previous: null })),
    });

    renderWithProviders(<PreviousPage />);

    await waitFor(() => {
      expect(screen.getByText('لا توجد إشعارات')).toBeInTheDocument();
      expect(screen.getByText('لم يتم العثور على أي إشعارات طوارئ')).toBeInTheDocument();
    });
  });

  it('displays correct emergency type labels', async () => {
    mockFetcher.mockResolvedValue({
      ok: true,
      text: () => Promise.resolve(JSON.stringify(mockEmergencyData)),
    });

    renderWithProviders(<PreviousPage />);

    await waitFor(() => {
      expect(screen.getByText('طبية')).toBeInTheDocument(); // Medical
      expect(screen.getByText('خطر')).toBeInTheDocument(); // Danger
    });
  });

  it('shows correct total count', async () => {
    mockFetcher.mockResolvedValue({
      ok: true,
      text: () => Promise.resolve(JSON.stringify(mockEmergencyData)),
    });

    renderWithProviders(<PreviousPage />);

    await waitFor(() => {
      expect(screen.getByText('عرض جميع إشعارات الطوارئ المرسلة سابقاً (2 إشعار)')).toBeInTheDocument();
    });
  });
});
