(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[813],{41907:(e,t,a)=>{"use strict";a.d(t,{H:()=>d});var r=a(56973),s=a(47956),l=a(6548),n=a(81467),i=a(12115),c=a(95155),o=(0,r.Rf)((e,t)=>{let{Component:a,children:o,getBaseProps:d}=function(e){var t;let[a,c]=(0,r.rE)(e,s.Q.variantKeys),{ref:o,as:d,children:m,className:h,style:u,size:x=40,offset:g=0,visibility:f="auto",isEnabled:p=!0,onVisibilityChange:y,...v}=a,b=(0,l.zD)(o);!function(e={}){let{domRef:t,isEnabled:a=!0,overflowCheck:r="vertical",visibility:s="auto",offset:l=0,onVisibilityChange:c,updateDeps:o=[]}=e,d=(0,i.useRef)(s);(0,i.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!a)return;let i=(t,a,r,l,i)=>{if("auto"===s){let t=`${l}${(0,n.ZH)(i)}Scroll`;a&&r?(e.dataset[t]="true",e.removeAttribute(`data-${l}-scroll`),e.removeAttribute(`data-${i}-scroll`)):(e.dataset[`${l}Scroll`]=a.toString(),e.dataset[`${i}Scroll`]=r.toString(),e.removeAttribute(`data-${l}-${i}-scroll`))}else{let e=a&&r?"both":a?l:r?i:"none";e!==d.current&&(null==c||c(e),d.current=e)}},o=()=>{for(let{type:t,prefix:a,suffix:s}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(r===t||"both"===r){let r="vertical"===t?e.scrollTop>l:e.scrollLeft>l,n="vertical"===t?e.scrollTop+e.clientHeight+l<e.scrollHeight:e.scrollLeft+e.clientWidth+l<e.scrollWidth;i(t,r,n,a,s)}},m=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute(`data-${t}-scroll`)})};return o(),e.addEventListener("scroll",o),"auto"!==s&&(m(),"both"===s?(e.dataset.topBottomScroll=String("vertical"===r),e.dataset.leftRightScroll=String("horizontal"===r)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset[`${t}Scroll`]=String(s===t)}))),()=>{e.removeEventListener("scroll",o),m()}},[...o,a,s,r,c,t])}({domRef:b,offset:g,visibility:f,isEnabled:p,onVisibilityChange:y,updateDeps:[m],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let j=(0,i.useMemo)(()=>(0,s.Q)({...c,className:h}),[(0,n.t6)(c),h]);return{Component:d||"div",styles:j,domRef:b,children:m,getBaseProps:function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:b,className:j,"data-orientation":null!=(t=e.orientation)?t:"vertical",style:{"--scroll-shadow-size":"".concat(x,"px"),...u,...a.style},...v,...a}}}}({...e,ref:t});return(0,c.jsx)(a,{...d(),children:o})});o.displayName="NextUI.ScrollShadow";var d=o},47956:(e,t,a)=>{"use strict";a.d(t,{Q:()=>r});var r=(0,a(69478).tv)({base:[],variants:{orientation:{vertical:["overflow-y-auto","data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],horizontal:["overflow-x-auto","data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}})},54369:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>B});var r=a(95155),s=a(73906),l=a(56973),n=(0,a(69478).tv)({slots:{base:["group","relative","overflow-hidden","bg-content3 dark:bg-content2","pointer-events-none","before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-[shimmer_2s_infinite]","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2","data-[loaded=true]:pointer-events-auto","data-[loaded=true]:overflow-visible","data-[loaded=true]:!bg-transparent","data-[loaded=true]:before:opacity-0 data-[loaded=true]:before:-z-10 data-[loaded=true]:before:animate-none","data-[loaded=true]:after:opacity-0"],content:["opacity-0","group-data-[loaded=true]:opacity-100"]},variants:{disableAnimation:{true:{base:"before:animate-none before:transition-none after:transition-none",content:"transition-none"},false:{base:"transition-background !duration-300",content:"transition-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{}}),i=a(81467),c=a(5712),o=a(672),d=a(12115),m=a(75894),h=(0,l.Rf)((e,t)=>{let{Component:a,children:s,getSkeletonProps:h,getContentProps:u}=function(e){var t,a;let r=(0,m.o)(),[s,h]=(0,l.rE)(e,n.variantKeys),{as:u,children:x,isLoaded:g=!1,className:f,classNames:p,...y}=s,v=null!=(a=null!=(t=e.disableAnimation)?t:null==r?void 0:r.disableAnimation)&&a,b=(0,d.useMemo)(()=>n({...h,disableAnimation:v}),[(0,i.t6)(h),v,x]),j=(0,c.$)(null==p?void 0:p.base,f);return{Component:u||"div",children:x,slots:b,classNames:p,getSkeletonProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-loaded":(0,o.sE)(g),className:b.base({class:(0,c.$)(j,null==e?void 0:e.className)}),...y}},getContentProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{className:b.content({class:(0,c.$)(null==p?void 0:p.content,null==e?void 0:e.className)})}}}}({...e});return(0,r.jsx)(a,{ref:t,...h(),children:(0,r.jsx)("div",{...u(),children:s})})});h.displayName="NextUI.Skeleton";var u=a(20340),x=a(41907),g=a(27290),f=a(54736),p=a(94554),y=a(51976),v=a(75525),b=a(1243),j=a(19946);let _=(0,j.A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),w=(0,j.A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),N=(0,j.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var k=a(14186),S=a(4516);let A=(0,j.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var E=a(6874),M=a.n(E),z=a(4766);let D=e=>{switch(e){case"O":return"طلب مساعدة";case"M":return"طبية";case"D":return"خطر";default:return"غير محدد"}},O=e=>{switch(e){case"O":return"primary";case"M":return"success";case"D":return"danger";default:return"default"}},I=e=>{switch(e){case"O":return y.A;case"M":return v.A;default:return b.A}},$=e=>{let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/6e4);if(a<1)return"الآن";if(a<60)return"منذ ".concat(a," دقيقة");let r=Math.floor(a/60);if(r<24)return"منذ ".concat(r," ساعة");let s=Math.floor(r/24);return"منذ ".concat(s," يوم")};function C(e){let{src:t,alt:a,hasMultipleImages:s,imageCount:l=0}=e,[n,i]=(0,d.useState)(!0),[c,o]=(0,d.useState)(!1);return(0,r.jsxs)("div",{className:"relative w-full h-48 overflow-hidden rounded-t-lg bg-gradient-to-br from-gray-50 to-gray-100",children:[n&&(0,r.jsx)("div",{className:"absolute inset-0 w-full h-full",children:(0,r.jsx)(h,{className:"w-full h-full rounded-t-lg",children:(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"})})}),(0,r.jsx)("div",{className:"relative w-full h-full",children:(0,r.jsx)(u.W,{src:t,alt:a,className:"object-cover w-full h-full transition-all duration-300 hover:scale-105 ".concat(n?"opacity-0":"opacity-100"),fallbackSrc:"/placeholder.svg?height=192&width=320",onLoad:()=>i(!1),onError:()=>{i(!1),o(!0)},loading:"lazy"})}),s&&!c&&l>1&&(0,r.jsxs)("div",{className:"absolute top-3 right-3 bg-black/80 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs flex items-center gap-1.5 shadow-lg",children:[(0,r.jsx)(_,{className:"w-3.5 h-3.5"}),(0,r.jsxs)("span",{className:"font-medium",children:[l," صور"]})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 hover:opacity-100 transition-all duration-300 flex items-center justify-center",children:(0,r.jsx)("div",{className:"bg-white/90 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-lg text-sm font-medium shadow-lg transform translate-y-2 hover:translate-y-0 transition-transform duration-300",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(w,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"انقر لعرض التفاصيل"})]})})}),c&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-gray-500",children:[(0,r.jsx)(w,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{className:"text-xs",children:"فشل تحميل الصورة"})]})})]})}function L(e){let{data:t=[],heading:a}=e,l=Array.isArray(t)?t:[];return(0,r.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(M(),{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,r.jsx)(N,{className:"w-5 h-5"})}),(0,r.jsx)("h2",{className:"text-xl font-bold",children:a})]})}),0===l.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-48 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,r.jsx)(b.A,{className:"w-12 h-12 text-gray-400 mb-2"}),(0,r.jsx)("p",{className:"text-gray-500 text-lg font-medium",children:"لا توجد تنبيهات حالياً"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"سيتم عرض الطلبات الجديدة هنا"})]}):(0,r.jsx)(x.H,{orientation:"horizontal",className:"flex gap-4 sm:gap-6 w-full overflow-x-auto pb-4 px-1",children:l.map(e=>{let t=I(e.emergency_type);return(0,r.jsx)(g.Z,{className:"flex-none w-[280px] sm:w-[320px] border border-gray-200 emergency-card-hover hover:border-gray-300 hover:shadow-lg transition-all duration-300 bg-white rounded-lg overflow-hidden",children:(0,r.jsxs)(f.U,{className:"p-0",children:[e.image?(0,r.jsx)(C,{src:e.image,alt:"صورة طوارئ ".concat(D(e.emergency_type)," من ").concat(e.user_first_name," ").concat(e.user_last_name," في ").concat(e.location),hasMultipleImages:e.image_count>1,imageCount:e.image_count}):(0,r.jsx)("div",{className:"w-full h-48 bg-gradient-to-br from-gray-50 to-gray-150 rounded-t-lg flex items-center justify-center border-b border-gray-100",children:(0,r.jsxs)("div",{className:"text-center p-6",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(t,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("p",{className:"text-gray-500 text-sm font-medium",children:"لا توجد صورة مرفقة"}),(0,r.jsx)("p",{className:"text-gray-400 text-xs mt-1",children:D(e.emergency_type)})]})}),(0,r.jsxs)("div",{className:"p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(p.R,{color:O(e.emergency_type),size:"sm",variant:"flat",startContent:(0,r.jsx)(t,{className:"w-3 h-3"}),children:D(e.emergency_type)}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,r.jsx)(k.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:$(e.created_at)})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-blue-600 text-sm font-medium",children:e.user_first_name.charAt(0)})}),(0,r.jsx)("div",{children:(0,r.jsxs)("h3",{className:"font-semibold text-sm text-gray-900",children:[e.user_first_name," ",e.user_last_name]})})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)(S.A,{className:"w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0"}),(0,r.jsx)("p",{className:"text-xs text-gray-600 line-clamp-2",children:e.location})]})]}),(0,r.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,r.jsxs)(s.aF,{children:[(0,r.jsxs)(s.g6,{className:"w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2",children:[(0,r.jsx)(A,{className:"w-4 h-4"}),"عرض التفاصيل"]}),(0,r.jsx)(s.cw,{children:(0,r.jsx)(s.$m,{children:(0,r.jsx)(z.A,{id:e.id})})})]},"modal-".concat(e.id))})]})]})},e.id)})})]})}let P=(0,j.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),H=(0,j.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);function F(e){let{}=e;return(0,r.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(g.Z,{children:(0,r.jsxs)(f.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,r.jsx)(P,{className:"w-6 h-6 text-blue-500"}),(0,r.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,r.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,r.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,r.jsx)(g.Z,{children:(0,r.jsxs)(f.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,r.jsx)(H,{className:"w-6 h-6 text-blue-500"}),(0,r.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,r.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,r.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,r.jsx)(g.Z,{children:(0,r.jsxs)(f.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,r.jsx)(S.A,{className:"w-6 h-6 text-blue-500"}),(0,r.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,r.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,r.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}var R=a(59434),T=a(66146);let U=(0,j.A)("Siren",[["path",{d:"M7 18v-6a5 5 0 1 1 10 0v6",key:"pcx96s"}],["path",{d:"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z",key:"1b4s83"}],["path",{d:"M21 12h1",key:"jtio3y"}],["path",{d:"M18.5 4.5 18 5",key:"g5sp9y"}],["path",{d:"M2 12h1",key:"1uaihz"}],["path",{d:"M12 2v1",key:"11qlp1"}],["path",{d:"m4.929 4.929.707.707",key:"1i51kw"}],["path",{d:"M12 12v6",key:"3ahymv"}]]),q=(0,j.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function B(e){let t=[{id:"offer-1",user_first_name:"أحمد",user_last_name:"السيد",location:"القدس - عين علي، شارع مدرسة الثورية - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200",emergency_type:"O",image_count:1},{id:"offer-2",user_first_name:"محمد",user_last_name:"إبراهيم",location:"رام الله - شارع الإرسال - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200",emergency_type:"O",image_count:2}],a=[{id:"medical-1",user_first_name:"سارة",user_last_name:"خالد",location:"بيت لحم - شارع المهد - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200",emergency_type:"M",image_count:1},{id:"medical-2",user_first_name:"فاطمة",user_last_name:"أحمد",location:"الخليل - البلدة القديمة - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200",emergency_type:"M",image_count:3}],s=[{id:"danger-1",user_first_name:"عمر",user_last_name:"محمود",location:"نابلس - شارع فيصل - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200",emergency_type:"D",image_count:1},{id:"danger-2",user_first_name:"ليلى",user_last_name:"حسن",location:"جنين - المخيم - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200",emergency_type:"D",image_count:2}],[l,n]=(0,d.useState)({results:[]}),[i,c]=(0,d.useState)({results:[]}),[o,m]=(0,d.useState)({results:[]}),[h,u]=(0,d.useState)(!0),[x,g]=(0,d.useState)(null);return(0,d.useEffect)(()=>{console.log("\uD83D\uDE80 useEffect triggered - starting data fetch"),(async()=>{u(!0),g(null);try{console.log("\uD83D\uDD04 Attempting to fetch emergency data from API...");let e=async e=>{try{var t;let a=await (0,R.G)(e,null,"GET");if(!a.ok)throw Error("API request failed with status ".concat(a.status));let r=await a.text(),s=r?JSON.parse(r):{results:[]};return console.log("✅ Successfully fetched data for ".concat(e,":"),{count:s.count||0,resultsLength:(null===(t=s.results)||void 0===t?void 0:t.length)||0}),s}catch(t){throw console.error("❌ Error fetching ".concat(e,":"),t),t}};console.log("\uD83C\uDF10 Fetching public emergency notifications...");let[t,a,r]=await Promise.all([e("/emergency/?emergency_type=O"),e("/emergency/?emergency_type=M"),e("/emergency/?emergency_type=D")]);n(t),c(a),m(r),console.log("\uD83C\uDF89 Successfully loaded public emergency data from API")}catch(e){console.error("❌ Failed to fetch emergency data, using sample data:",e),g("Failed to load emergency data"),n({results:t}),c({results:a}),m({results:s})}finally{u(!1)}})()},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]",children:(0,r.jsxs)("div",{id:"send-emergency",className:"max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center",children:[(0,r.jsx)(U,{className:"absolute top-3 left-3"}),(0,r.jsx)("h3",{children:"أرسل تنبيهًا للطوارئ"}),(0,r.jsx)("p",{children:"حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن."}),(0,r.jsx)(T.T,{as:M(),href:"/add-application",endContent:(0,r.jsx)(q,{}),className:"mx-auto",color:"danger",children:"أرسل إشعار للطوارئ"})]})}),(0,r.jsxs)("section",{id:"recieved-emergency",className:"mt-[65vh]",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-center",children:"إشعارات الطوارئ المستلمة"}),h?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"جاري تحميل الإشعارات..."})]}):x?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-red-500 mb-4",children:"حدث خطأ في تحميل البيانات"}),(0,r.jsx)("p",{className:"text-gray-600",children:"سيتم عرض البيانات التجريبية"})]}):null,(0,r.jsx)(L,{data:l.results,heading:"طلبات التنبيه حول الإغاثة"}),(0,r.jsx)(L,{data:i.results,heading:"طلبات التنبيه حول الصحة"}),(0,r.jsx)(L,{data:o.results,heading:"طلبات التنبيه حول الخطر"})]}),(0,r.jsx)(F,{})]})}},67357:(e,t,a)=>{Promise.resolve().then(a.bind(a,54369))}},e=>{var t=t=>e(e.s=t);e.O(0,[146,688,829,686,874,421,333,441,684,358],()=>t(67357)),_N_E=e.O()}]);