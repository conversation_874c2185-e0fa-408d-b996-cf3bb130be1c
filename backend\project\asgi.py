import os
from channels.routing import ProtocolType<PERSON>out<PERSON>, URLRouter
from channels.security.websocket import AllowedHostsOriginValidator
from django.core.asgi import get_asgi_application
from chat.routing import websocket_urlpatterns  
from .middleware import JwtAuthMiddlewareStack as AuthMiddlewareStack 

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings.debug')

application = ProtocolTypeRouter(
    {
        "http": get_asgi_application(),
        "websocket": AllowedHostsOriginValidator(
            AuthMiddlewareStack(URLRouter(websocket_urlpatterns)),
        ),
    }
)