"use client"
import { type ResetPasswordSchemaType, resetPasswordSchema } from "@/schemas/auth"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { Eye, EyeOff } from "lucide-react"
import { useState } from "react"
import { Controller, useForm } from "react-hook-form"

type Props = {}

export default function ResetPasswordForm({}: Props) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordSchemaType>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  })

  function onSubmit(values: ResetPasswordSchemaType) {
    console.log(values)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-6">
      <Controller
        name="password"
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            type={showPassword ? "text" : "password"}
            label="كلمة المرور"
            variant="bordered"
            isInvalid={!!errors.password}
            errorMessage={errors.password?.message}
            endContent={
              <button type="button" className="h-full pl-2" onClick={() => setShowPassword(!showPassword)}>
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            }
          />
        )}
      />

      <Controller
        name="confirmPassword"
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            type={showConfirmPassword ? "text" : "password"}
            label="تأكيد كلمة المرور"
            variant="bordered"
            isInvalid={!!errors.confirmPassword}
            errorMessage={errors.confirmPassword?.message}
            endContent={
              <button type="button" className="h-full pl-2" onClick={() => setShowConfirmPassword(!showPassword)}>
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            }
          />
        )}
      />

      <Button type="submit" color="primary" className="w-full">
        تسجيل الدخول
      </Button>
    </form>
  )
}
