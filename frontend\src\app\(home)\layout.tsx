"use client";

import Footer from "@/components/global/Footer";
import Header from "@/components/global/Header";
import { Chat } from "@/components/specific/Chat";
import { fetcher } from "@/lib/utils";
import { Suspense } from "react";
import { useSearchParams } from "next/navigation";
import type React from "react";
import { createContext, useEffect, useState } from "react";
import { useCookies } from "react-cookie";
import { toast } from "sonner";

type Props = { children: React.ReactNode };

type UserContext = {
  isValid: boolean;
  isLoading: boolean;
  user: User | null;
};

export type User = {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  is_admin: boolean;
};

export const UserContext = createContext<UserContext | null>(null);

function ErrorHandler() {
  const params = useSearchParams();
  const error = params?.get("error");

  useEffect(() => {
    if (error === "not-logged-in") {
      toast.error("يجب عليك تسجيل الدخول للوصول إلى هذه الصفحة");
    }
  }, [error]);

  return null;
}

export default function HomeLayout({ children }: Props) {
  const [isLoading, setIsLoading] = useState(true);
  const [isValid, setIsValid] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [cookies] = useCookies(["access", "refresh"]);

  useEffect(() => {
    const verifyToken = async () => {
      try {
        if (!cookies.access) {
          setIsValid(false);
          setIsLoading(false);
          return;
        }

        const res = await fetcher(
          "/auth/jwt/verify/",
          {
            token: cookies.access,
          },
          "POST"
        );

        if (res.status === 200) {
          setIsValid(true);
          const userRes = await fetcher(
            "/users/me/",
            null,
            "GET",
            cookies.access
          );
          const userData = await userRes.json();
          setUser(userData);
        } else {
          setIsValid(false);
        }
      } catch (error) {
        console.error("Error verifying token:", error);
        setIsValid(false);
      } finally {
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [cookies.access]);

  return (
    <UserContext.Provider value={{ isValid, isLoading, user }}>
      <div className="flex flex-col min-h-screen relative">
        <Header />
        <main className="flex-1">
          <Suspense fallback={null}>
            <ErrorHandler />
          </Suspense>
          {children}
        </main>
        <Footer />
        <Chat />
      </div>
    </UserContext.Provider>
  );
}
