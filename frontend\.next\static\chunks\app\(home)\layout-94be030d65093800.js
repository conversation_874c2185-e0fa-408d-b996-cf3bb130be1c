(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[790],{3848:(e,s,r)=>{"use strict";r.r(s),r.d(s,{UserContext:()=>G,default:()=>C});var t=r(95155);let a=[{link:"/",text:"الصفحة الرئيسية"},{link:"/#send-emergency",text:"اشعار طوارئ"},{link:"/#recieved-emergency",text:"الاشعارات المستلمه"},{link:"/previous",text:"الإشعارات السابقة"},{link:"/#call-us",text:"اتصل بنا"}];var l=r(10488),n=r(18175),c=r(72894),i=r(2925),d=r(75684),o=r(6874),x=r.n(o);function m(){let e=[{icon:l.A,href:"#"},{icon:n.A,href:"#"},{icon:c.A,href:"#"},{icon:i.A,href:"#"},{icon:d.A,href:"#"}];return(0,t.jsx)("footer",{className:"bg-gray-100",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8",children:[(0,t.jsxs)("div",{className:"gap-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold text-blue-600",children:"نداء الوطن"}),(0,t.jsx)("p",{className:"text-gray-600",children:"صوتك في الطوارئ"})]}),(0,t.jsx)("div",{className:"h-[3rem] w-px bg-primary-700 hidden xl:block"}),(0,t.jsxs)("div",{className:"text-center mb-8 max-w-3xl mx-auto text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:['منصة "',(0,t.jsx)("span",{className:"font-bold text-primary-400",children:"نداء الوطن"}),'" هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن مخاطر تهددهم']}),"sp"]}),(0,t.jsx)("div",{className:"h-[3rem] w-px bg-primary-700 hidden xl:block"}),(0,t.jsxs)("div",{className:"grid gap-y-2",children:[(0,t.jsx)("p",{children:"وسائل التواصل الاجتماعي "}),(0,t.jsx)("div",{className:"flex gap-4",children:e.map((e,s)=>{let r=e.icon;return(0,t.jsx)(x(),{href:e.href,className:"w-8 h-8 flex items-center justify-center rounded-full bg-white text-blue-500 hover:bg-blue-50 transition-colors",children:(0,t.jsx)(r,{className:"w-4 h-4"})},s)})})]})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-gray-200",children:[(0,t.jsx)("nav",{children:(0,t.jsx)("ul",{className:"flex flex-wrap justify-center gap-4",children:a.map((e,s)=>(0,t.jsx)("li",{children:(0,t.jsx)(x(),{href:e.link,className:"text-sm text-gray-600 hover:text-blue-500",children:e.text})},s))})}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4 md:mb-0",children:"جميع حقوق النشر محفوظة لدى نداء الوطن \xa9 2025"})]})]})})}var u=r(66146),h=r(81495),f=r(12115),g=r(82842);function j(e){let[s,r,l]=(0,g.lT)(),n=(0,f.useContext)(G),c=null==n?void 0:n.user;return(0,t.jsxs)("header",{className:"sticky m-8 px-6 py-4 flex justify-between backdrop-blur-sm rounded-xl z-[999]",children:[(0,t.jsx)(h.h,{href:"/",children:(0,t.jsx)("h1",{className:"font-extrabold",children:"نداء الوطن"})}),(0,t.jsx)("div",{className:"flex gap-3",children:a.map(e=>{let{link:s,text:r}=e;return(0,t.jsx)(h.h,{href:s,className:"text-black hover:text-black/75",children:r},s)})}),(null==n?void 0:n.isLoading)?(0,t.jsx)("p",{children:"جارى التحميل"}):c&&n.isValid?(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("p",{className:"mt-3",children:["مرحبا ",c.first_name+" "+c.last_name]}),(0,t.jsx)(u.T,{as:h.h,href:"/",variant:"bordered",color:"danger",onPress:()=>{l("access"),l("refresh")},children:"تسجيل الخروج"})]}):(0,t.jsx)(u.T,{as:h.h,href:"/auth",variant:"bordered",color:"primary",children:"تسجيل الدخول"})]})}var p=r(59434),v=r(55594);let b=v.Ik({message:v.Yj().min(1,"يرجى إدخال رسالة")});var N=r(90221),y=r(93176),w=r(74311),I=r(12486),k=r(41788),T=r(62177);let S=[{id:1,userId:1,message:"مرحباً"},{id:2,userId:2,message:"أهلاً"},{id:3,userId:1,message:"كيف حالك؟"},{id:4,userId:2,message:"أنا بخير، شكراً! ماذا عنك؟"},{id:5,userId:1,message:"أنا بخير أيضاً. أعمل على مشروع حالياً."},{id:6,userId:2,message:"هذا رائع! ما نوع المشروع؟"},{id:7,userId:1,message:"إنه تطبيق ويب لإدارة المهام."},{id:8,userId:2,message:"يبدو مثيراً للاهتمام! هل تستخدم React في ذلك؟"},{id:9,userId:1,message:"نعم، أستخدم React و Node.js. أحب العمل بهما."},{id:10,userId:2,message:"وأنا كذلك! أستخدمهما في معظم مشاريعي أيضاً."},{id:11,userId:1,message:"هل لديك أي نصائح لتحسين الأداء في React؟"},{id:12,userId:2,message:"بالطبع! استخدم React.memo و React.useMemo للحسابات الثقيلة."},{id:13,userId:1,message:"شكراً! يبدو ذلك مفيداً. سأجربه."},{id:14,userId:2,message:"على الرحب والسعة. أخبرني إذا احتجت إلى أي مساعدة."},{id:15,userId:1,message:"سأفعل. شكراً لدعمك!"},{id:16,userId:2,message:"لا مشكلة! ما التقنيات الأخرى التي تستخدمها؟"},{id:17,userId:1,message:"أستخدم أيضاً PostgreSQL لإدارة قاعدة البيانات."},{id:18,userId:2,message:"اختيار رائع! PostgreSQL قوي جداً للبيانات العلاقية."},{id:19,userId:1,message:"بالتأكيد! تعلمت الكثير أثناء العمل على هذا المشروع."},{id:20,userId:2,message:"هذه أفضل ميزة في بناء المشاريع. تتعلم الكثير!"}];function A(e){let{}=e,[s]=(0,g.lT)(),r=(0,f.useRef)(null),[a,l]=(0,f.useState)(!1),n=s.access,{control:c,handleSubmit:i,formState:{errors:d,isSubmitting:o},reset:x}=(0,T.mN)({resolver:(0,N.u)(b),defaultValues:{message:""}}),[m,h]=(0,f.useState)(S),j=()=>{var e;null===(e=r.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};return(0,f.useEffect)(()=>{n&&(async()=>{try{let e=await (0,p.G)("/chats/messages/",null,"GET",n);await e.json(),j()}catch(e){console.error("Failed to fetch messages:",e)}})()},[n]),(0,f.useEffect)(()=>{j()},[m]),(0,t.jsx)(t.Fragment,{children:a?(0,t.jsxs)("div",{className:"h-[500px] w-[400px] bg-slate-200 fixed bottom-0 right-0 rounded-xl overflow-clip flex flex-col",children:[(0,t.jsxs)("header",{className:"flex justify-between items-center bg-blue-600 text-white py-3 px-2",children:[(0,t.jsx)("h2",{className:"font-semibold text-lg",children:"خدمة العملاء"}),(0,t.jsx)(u.T,{className:"bg-black/75 p-1 rounded-full min-w-8",onPress:()=>l(!1),children:(0,t.jsx)(w.A,{className:"text-white size-4"})})]}),(0,t.jsx)("div",{className:"flex-1 h-[23.5rem] overflow-y-auto",children:(null==m?void 0:m.length)===0?(0,t.jsx)("div",{className:"grid place-content-center h-full",children:(0,t.jsx)("p",{children:"لا يوجد لديك رسائل"})}):(0,t.jsxs)("div",{className:"flex flex-col gap-2 pt-2 justify-end",children:[m.map(e=>(0,t.jsx)("div",{className:(0,p.cn)("flex flex-col mb-2",1===e.userId?"items-start":"items-end"),children:(0,t.jsx)("p",{className:(0,p.cn)("max-w-[250px] px-4 py-2 rounded-xl text-white text-sm",1===e.userId?"bg-red-500 rounded-tl-none":"bg-black/25 rounded-tr-none"),children:e.message})},e.id)),(0,t.jsx)("div",{ref:r})]})}),(0,t.jsxs)("form",{className:"flex gap-2 px-2 border-t-1 pt-3 pb-2",onSubmit:i(e=>{h(s=>[...s,{id:Date.now(),userId:1,message:e.message}]),x()}),children:[(0,t.jsx)(T.xI,{name:"message",control:c,render:e=>{var s;let{field:r}=e;return(0,t.jsx)(y.r,{...r,placeholder:"أدخل رسالتك",isInvalid:!!d.message,errorMessage:null===(s=d.message)||void 0===s?void 0:s.message})}}),(0,t.jsx)(u.T,{type:"submit",disabled:o,className:"min-w-8 bg-blue-600",children:(0,t.jsx)(I.A,{className:"text-white"})})]})]}):(0,t.jsx)(u.T,{onPress:()=>l(!0),className:"fixed top-[92vh] right-2 w-16 h-16 px-0 bg-blue-600 text-white rounded-full text-xl font-bold grid place-content-center",children:(0,t.jsx)(k.A,{className:"size-8"})})})}var E=r(35695),P=r(56671);let G=(0,f.createContext)(null);function _(){let e=(0,E.useSearchParams)(),s=null==e?void 0:e.get("error");return(0,f.useEffect)(()=>{"not-logged-in"===s&&P.o.error("يجب عليك تسجيل الدخول للوصول إلى هذه الصفحة")},[s]),null}function C(e){let{children:s}=e,[r,a]=(0,f.useState)(!0),[l,n]=(0,f.useState)(!1),[c,i]=(0,f.useState)(null),[d]=(0,g.lT)(["access","refresh"]);return(0,f.useEffect)(()=>{(async()=>{try{if(!d.access){n(!1),a(!1);return}let e=await (0,p.G)("/auth/jwt/verify/",{token:d.access},"POST");if(200===e.status){n(!0);let e=await (0,p.G)("/users/me/",null,"GET",d.access),s=await e.json();i(s)}else n(!1)}catch(e){console.error("Error verifying token:",e),n(!1)}finally{a(!1)}})()},[d.access]),(0,t.jsx)(G.Provider,{value:{isValid:l,isLoading:r,user:c},children:(0,t.jsxs)("div",{className:"flex flex-col min-h-screen relative",children:[(0,t.jsx)(j,{}),(0,t.jsxs)("main",{className:"flex-1",children:[(0,t.jsx)(f.Suspense,{fallback:null,children:(0,t.jsx)(_,{})}),s]}),(0,t.jsx)(m,{}),(0,t.jsx)(A,{})]})})}},17908:(e,s,r)=>{Promise.resolve().then(r.bind(r,3848))},59434:(e,s,r)=>{"use strict";r.d(s,{G:()=>n,cn:()=>l});var t=r(52596),a=r(39688);function l(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}async function n(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",t=arguments.length>3?arguments[3]:void 0,a={"Content-Type":"application/json"};t&&(a.Authorization="Bearer ".concat(t));let l={method:r,headers:a,next:{revalidate:60}};s&&"GET"!==r&&(l.body=JSON.stringify(s));try{let s="".concat("http://localhost:8000").concat(e);console.log("Fetching: ".concat(s));let r=await fetch(s,l);return r.ok||console.warn("API request failed: ".concat(s," returned status ").concat(r.status)),r}catch(e){throw console.error("API request failed:",e),e}}}},e=>{var s=s=>e(e.s=s);e.O(0,[146,688,314,671,842,874,419,441,684,358],()=>s(17908)),_N_E=e.O()}]);