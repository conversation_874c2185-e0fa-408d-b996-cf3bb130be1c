"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[421],{1243:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(19946).A)("<PERSON>Alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1529:(e,a,r)=>{r.d(a,{o:()=>t});var s=r(95155),t=e=>(0,s.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:(0,s.jsx)("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})})},4516:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},14186:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},20340:(e,a,r)=>{r.d(a,{W:()=>h});var s=r(12115),t=r(75894),l=r(56973),o=(0,r(69478).tv)({slots:{wrapper:"relative shadow-black/5",zoomedWrapper:"relative overflow-hidden rounded-inherit",img:"relative z-10 opacity-0 shadow-black/5 data-[loaded=true]:opacity-100",blurredImg:["absolute","z-0","inset-0","w-full","h-full","object-cover","filter","blur-lg","scale-105","saturate-150","opacity-30","translate-y-1"]},variants:{radius:{none:{},sm:{},md:{},lg:{},full:{}},shadow:{none:{wrapper:"shadow-none",img:"shadow-none"},sm:{wrapper:"shadow-small",img:"shadow-small"},md:{wrapper:"shadow-medium",img:"shadow-medium"},lg:{wrapper:"shadow-large",img:"shadow-large"}},isZoomed:{true:{img:["object-cover","transform","hover:scale-125"]}},showSkeleton:{true:{wrapper:["group","relative","overflow-hidden","bg-content3 dark:bg-content2"],img:"opacity-0"}},disableAnimation:{true:{img:"transition-none"},false:{img:"transition-transform-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{radius:"lg",shadow:"none",isZoomed:!1,isBlurred:!1,showSkeleton:!1},compoundVariants:[{showSkeleton:!0,disableAnimation:!1,class:{wrapper:["before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-[shimmer_2s_infinite]","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2"]}}],compoundSlots:[{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"none",class:"rounded-none"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"full",class:"rounded-full"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"sm",class:"rounded-small"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"md",class:"rounded-md"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"lg",class:"rounded-large"}]}),n=r(6548),i=r(81467),d=r(5712),c=r(672),u=r(27905),b=r(95155),m=(0,l.Rf)((e,a)=>{let{Component:r,domRef:m,slots:h,classNames:p,isBlurred:f,isZoomed:g,fallbackSrc:v,removeWrapper:w,disableSkeleton:y,getImgProps:k,getWrapperProps:x,getBlurredImgProps:C}=function(e){var a,r;let b=(0,t.o)(),[m,h]=(0,l.rE)(e,o.variantKeys),{ref:p,as:f,src:g,className:v,classNames:w,loading:y,isBlurred:k,fallbackSrc:x,isLoading:C,disableSkeleton:z=!!x,removeWrapper:E=!1,onError:N,onLoad:A,srcSet:j,sizes:P,crossOrigin:I,...M}=m,D=function(e={}){let{onLoad:a,onError:r,ignoreFallback:t}=e,l=s.useSyncExternalStore(()=>()=>{},()=>!0,()=>!1),o=(0,s.useRef)(l?new Image:null),[n,i]=(0,s.useState)("pending");(0,s.useEffect)(()=>{o.current&&(o.current.onload=e=>{d(),i("loaded"),null==a||a(e)},o.current.onerror=e=>{d(),i("failed"),null==r||r(e)})},[o.current]);let d=()=>{o.current&&(o.current.onload=null,o.current.onerror=null,o.current=null)};return(0,u.U)(()=>{l&&i(function(e,a){let{loading:r,src:s,srcSet:t,crossOrigin:l,sizes:o,ignoreFallback:n}=e;if(!s)return"pending";if(n)return"loaded";let i=new Image;return(i.src=s,l&&(i.crossOrigin=l),t&&(i.srcset=t),o&&(i.sizes=o),r&&(i.loading=r),a.current=i,i.complete&&i.naturalWidth)?"loaded":"loading"}(e,o))},[l]),t?"loaded":n}({src:g,loading:y,onError:N,onLoad:A,ignoreFallback:!1,srcSet:j,sizes:P,crossOrigin:I}),B=null!=(r=null!=(a=e.disableAnimation)?a:null==b?void 0:b.disableAnimation)&&r,S="loaded"===D&&!C,W="loading"===D||C,V=e.isZoomed,$=(0,n.zD)(p),{w:H,h:R}=(0,s.useMemo)(()=>({w:m.width?"number"==typeof m.width?"".concat(m.width,"px"):m.width:"fit-content",h:m.height?"number"==typeof m.height?"".concat(m.height,"px"):m.height:"auto"}),[null==m?void 0:m.width,null==m?void 0:m.height]),U=(!g||!S)&&!!x,F=W&&!z,O=(0,s.useMemo)(()=>o({...h,disableAnimation:B,showSkeleton:F}),[(0,i.t6)(h),B,F]),T=(0,d.$)(v,null==w?void 0:w.img),Z=(0,s.useCallback)(()=>{let e=U?{backgroundImage:"url(".concat(x,")")}:{};return{className:O.wrapper({class:null==w?void 0:w.wrapper}),style:{...e,maxWidth:H}}},[O,U,x,null==w?void 0:w.wrapper,H]),_=(0,s.useCallback)(()=>({src:g,"aria-hidden":(0,c.sE)(!0),className:O.blurredImg({class:null==w?void 0:w.blurredImg})}),[O,g,null==w?void 0:w.blurredImg]);return{Component:f||"img",domRef:$,slots:O,classNames:w,isBlurred:k,disableSkeleton:z,fallbackSrc:x,removeWrapper:E,isZoomed:V,isLoading:W,getImgProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=(0,d.$)(T,null==e?void 0:e.className);return{src:g,ref:$,"data-loaded":(0,c.sE)(S),className:O.img({class:a}),loading:y,srcSet:j,sizes:P,crossOrigin:I,...M,style:{...(null==M?void 0:M.height)&&{height:R},...e.style,...M.style}}},getWrapperProps:Z,getBlurredImgProps:_}}({...e,ref:a}),z=(0,b.jsx)(r,{ref:m,...k()});if(w)return z;let E=(0,b.jsx)("div",{className:h.zoomedWrapper({class:null==p?void 0:p.zoomedWrapper}),children:z});return f?(0,b.jsxs)("div",{...x(),children:[g?E:z,(0,s.cloneElement)(z,C())]}):g||!y||v?(0,b.jsxs)("div",{...x(),children:[" ",g?E:z]}):z});m.displayName="NextUI.Image";var h=m},27290:(e,a,r)=>{r.d(a,{Z:()=>z});var s=r(65262),t=r(69478),l=r(66232),o=(0,t.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...l.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),n=r(12115),i=r(73750),d=r(81627),c=r(77151),u=r(9906),b=r(88629),m=r(75894),h=r(56973),p=r(5712),f=r(81467),g=r(672),v=r(491),w=r(6548),y=r(35925),k=r(53580),x=r(95155),C=(0,h.Rf)((e,a)=>{let{children:r,context:t,Component:l,isPressable:C,disableAnimation:z,disableRipple:E,getCardProps:N,getRippleProps:A}=function(e){var a,r,s,t;let l=(0,m.o)(),[k,x]=(0,h.rE)(e,o.variantKeys),{ref:C,as:z,children:E,onClick:N,onPress:A,autoFocus:j,className:P,classNames:I,allowTextSelectionOnPress:M=!0,...D}=k,B=(0,w.zD)(C),S=z||(e.isPressable?"button":"div"),W="string"==typeof S,V=null!=(r=null!=(a=e.disableAnimation)?a:null==l?void 0:l.disableAnimation)&&r,$=null!=(t=null!=(s=e.disableRipple)?s:null==l?void 0:l.disableRipple)&&t,H=(0,p.$)(null==I?void 0:I.base,P),{onClear:R,onPress:U,ripples:F}=(0,y.k)(),O=(0,n.useCallback)(e=>{$||V||!B.current||U(e)},[$,V,B,U]),{buttonProps:T,isPressed:Z}=(0,b.l)({onPress:(0,i.c)(A,O),elementType:z,isDisabled:!e.isPressable,onClick:N,allowTextSelectionOnPress:M,...D},B),{hoverProps:_,isHovered:q}=(0,u.M)({isDisabled:!e.isHoverable,...D}),{isFocusVisible:K,isFocused:L,focusProps:J}=(0,c.o)({autoFocus:j}),G=(0,n.useMemo)(()=>o({...x,disableAnimation:V}),[(0,f.t6)(x),V]),Q=(0,n.useMemo)(()=>({slots:G,classNames:I,disableAnimation:V,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[G,I,e.isDisabled,e.isFooterBlurred,V,e.fullWidth]),X=(0,n.useCallback)(function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:B,className:G.base({class:H}),tabIndex:e.isPressable?0:-1,"data-hover":(0,g.sE)(q),"data-pressed":(0,g.sE)(Z),"data-focus":(0,g.sE)(L),"data-focus-visible":(0,g.sE)(K),"data-disabled":(0,g.sE)(e.isDisabled),...(0,d.v)(e.isPressable?{...T,...J,role:"button"}:{},e.isHoverable?_:{},(0,v.$)(D,{enabled:W}),(0,v.$)(a))}},[B,G,H,W,e.isPressable,e.isHoverable,e.isDisabled,q,Z,K,T,J,_,D]),Y=(0,n.useCallback)(()=>({ripples:F,onClear:R}),[F,R]);return{context:Q,domRef:B,Component:S,classNames:I,children:E,isHovered:q,isPressed:Z,disableAnimation:V,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:$,handlePress:O,isFocusVisible:K,getCardProps:X,getRippleProps:Y}}({...e,ref:a});return(0,x.jsxs)(l,{...N(),children:[(0,x.jsx)(s.u,{value:t,children:r}),C&&!z&&!E&&(0,x.jsx)(k.j,{...A()})]})});C.displayName="NextUI.Card";var z=C},27905:(e,a,r)=>{r.d(a,{U:()=>t});var s=r(12115),t=(null==globalThis?void 0:globalThis.document)?s.useLayoutEffect:s.useEffect},36545:(e,a,r)=>{r.d(a,{P:()=>u});var s=r(14060),t=r(96666),l=r(7038),o=r(12912),n=r(53292),i=r(53880),d=r(71277);let c=(0,i.C)({...t.W,...o.n,...l.$,...n.Z},d.J),u=(0,s.I)(c)},51976:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},54736:(e,a,r)=>{r.d(a,{U:()=>d});var s=r(65262),t=r(56973),l=r(6548),o=r(5712),n=r(95155),i=(0,t.Rf)((e,a)=>{var r;let{as:t,className:i,children:d,...c}=e,u=(0,l.zD)(a),{slots:b,classNames:m}=(0,s.f)(),h=(0,o.$)(null==m?void 0:m.body,i);return(0,n.jsx)(t||"div",{ref:u,className:null==(r=b.body)?void 0:r.call(b,{class:h}),...c,children:d})});i.displayName="NextUI.CardBody";var d=i},64924:(e,a,r)=>{r.d(a,{y:()=>d});var s=r(491),t=(0,r(69478).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),l=r(12115),o=r(56973),n=r(95155),i=(0,o.Rf)((e,a)=>{let{Component:r,getDividerProps:o}=function(e){var a;let r,o;let{as:n,className:i,orientation:d,...c}=e,u=n||"hr";"hr"===u&&"vertical"===d&&(u="div");let{separatorProps:b}=(a={elementType:"string"==typeof u?u:"hr",orientation:d},o=(0,s.$)(a,{enabled:"string"==typeof a.elementType}),("vertical"===a.orientation&&(r="vertical"),"hr"!==a.elementType)?{separatorProps:{...o,role:"separator","aria-orientation":r}}:{separatorProps:o}),m=(0,l.useMemo)(()=>t({orientation:d,className:i}),[d,i]);return{Component:u,getDividerProps:(0,l.useCallback)((e={})=>({className:m,role:"separator","data-orientation":d,...b,...c,...e}),[m,d,b,c])}}({...e});return(0,n.jsx)(r,{ref:a,...o()})});i.displayName="NextUI.Divider";var d=i},65262:(e,a,r)=>{r.d(a,{f:()=>t,u:()=>s});var[s,t]=(0,r(42810).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},71007:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},94554:(e,a,r)=>{r.d(a,{R:()=>v});var s=r(56973),t=r(81627),l=r(19914),o=r(77151),n=r(70418),i=r(69478),d=r(66232),c=(0,i.tv)({slots:{base:["relative","max-w-fit","min-w-min","inline-flex","items-center","justify-between","box-border","whitespace-nowrap"],content:"flex-1 text-inherit font-normal",dot:["w-2","h-2","ml-1","rounded-full"],avatar:"flex-shrink-0",closeButton:["z-10","appearance-none","outline-none","select-none","transition-opacity","opacity-70","hover:opacity-100","cursor-pointer","active:opacity-disabled","tap-highlight-transparent"]},variants:{variant:{solid:{},bordered:{base:"border-medium bg-transparent"},light:{base:"bg-transparent"},flat:{},faded:{base:"border-medium"},shadow:{},dot:{base:"border-medium border-default text-foreground bg-transparent"}},color:{default:{dot:"bg-default-400"},primary:{dot:"bg-primary"},secondary:{dot:"bg-secondary"},success:{dot:"bg-success"},warning:{dot:"bg-warning"},danger:{dot:"bg-danger"}},size:{sm:{base:"px-1 h-6 text-tiny",content:"px-1",closeButton:"text-medium",avatar:"w-4 h-4"},md:{base:"px-1 h-7 text-small",content:"px-2",closeButton:"text-large",avatar:"w-5 h-5"},lg:{base:"px-2 h-8 text-medium",content:"px-2",closeButton:"text-xl",avatar:"w-6 h-6"}},radius:{none:{base:"rounded-none"},sm:{base:"rounded-small"},md:{base:"rounded-medium"},lg:{base:"rounded-large"},full:{base:"rounded-full"}},isOneChar:{true:{},false:{}},isCloseable:{true:{},false:{}},hasStartContent:{true:{}},hasEndContent:{true:{}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},isCloseButtonFocusVisible:{true:{closeButton:[...d.$1,"ring-1","rounded-full"]}}},defaultVariants:{variant:"solid",color:"default",size:"md",radius:"full",isDisabled:!1},compoundVariants:[{variant:"solid",color:"default",class:{base:n.k.solid.default}},{variant:"solid",color:"primary",class:{base:n.k.solid.primary}},{variant:"solid",color:"secondary",class:{base:n.k.solid.secondary}},{variant:"solid",color:"success",class:{base:n.k.solid.success}},{variant:"solid",color:"warning",class:{base:n.k.solid.warning}},{variant:"solid",color:"danger",class:{base:n.k.solid.danger}},{variant:"shadow",color:"default",class:{base:n.k.shadow.default}},{variant:"shadow",color:"primary",class:{base:n.k.shadow.primary}},{variant:"shadow",color:"secondary",class:{base:n.k.shadow.secondary}},{variant:"shadow",color:"success",class:{base:n.k.shadow.success}},{variant:"shadow",color:"warning",class:{base:n.k.shadow.warning}},{variant:"shadow",color:"danger",class:{base:n.k.shadow.danger}},{variant:"bordered",color:"default",class:{base:n.k.bordered.default}},{variant:"bordered",color:"primary",class:{base:n.k.bordered.primary}},{variant:"bordered",color:"secondary",class:{base:n.k.bordered.secondary}},{variant:"bordered",color:"success",class:{base:n.k.bordered.success}},{variant:"bordered",color:"warning",class:{base:n.k.bordered.warning}},{variant:"bordered",color:"danger",class:{base:n.k.bordered.danger}},{variant:"flat",color:"default",class:{base:n.k.flat.default}},{variant:"flat",color:"primary",class:{base:n.k.flat.primary}},{variant:"flat",color:"secondary",class:{base:n.k.flat.secondary}},{variant:"flat",color:"success",class:{base:n.k.flat.success}},{variant:"flat",color:"warning",class:{base:n.k.flat.warning}},{variant:"flat",color:"danger",class:{base:n.k.flat.danger}},{variant:"faded",color:"default",class:{base:n.k.faded.default}},{variant:"faded",color:"primary",class:{base:n.k.faded.primary}},{variant:"faded",color:"secondary",class:{base:n.k.faded.secondary}},{variant:"faded",color:"success",class:{base:n.k.faded.success}},{variant:"faded",color:"warning",class:{base:n.k.faded.warning}},{variant:"faded",color:"danger",class:{base:n.k.faded.danger}},{variant:"light",color:"default",class:{base:n.k.light.default}},{variant:"light",color:"primary",class:{base:n.k.light.primary}},{variant:"light",color:"secondary",class:{base:n.k.light.secondary}},{variant:"light",color:"success",class:{base:n.k.light.success}},{variant:"light",color:"warning",class:{base:n.k.light.warning}},{variant:"light",color:"danger",class:{base:n.k.light.danger}},{isOneChar:!0,hasStartContent:!1,hasEndContent:!1,size:"sm",class:{base:"w-5 h-5 min-w-5 min-h-5"}},{isOneChar:!0,hasStartContent:!1,hasEndContent:!1,size:"md",class:{base:"w-6 h-6 min-w-6 min-h-6"}},{isOneChar:!0,hasStartContent:!1,hasEndContent:!1,size:"lg",class:{base:"w-7 h-7 min-w-7 min-h-7"}},{isOneChar:!0,isCloseable:!1,hasStartContent:!1,hasEndContent:!1,class:{base:"px-0 justify-center",content:"px-0 flex-none"}},{isOneChar:!0,isCloseable:!0,hasStartContent:!1,hasEndContent:!1,class:{base:"w-auto"}},{isOneChar:!0,variant:"dot",class:{base:"w-auto h-7 px-1 items-center",content:"px-2"}},{hasStartContent:!0,size:"sm",class:{content:"pl-0.5"}},{hasStartContent:!0,size:["md","lg"],class:{content:"pl-1"}},{hasEndContent:!0,size:"sm",class:{content:"pr-0.5"}},{hasEndContent:!0,size:["md","lg"],class:{content:"pr-1"}}]}),u=r(6548),b=r(5712),m=r(81467),h=r(12115),p=r(1529),f=r(95155),g=(0,s.Rf)((e,a)=>{let{Component:r,children:n,slots:i,classNames:d,isDot:g,isCloseable:v,startContent:w,endContent:y,getCloseButtonProps:k,getChipProps:x}=function(e){let[a,r]=(0,s.rE)(e,c.variantKeys),{ref:n,as:i,children:d,avatar:p,startContent:f,endContent:g,onClose:v,classNames:w,className:y,...k}=a,x=(0,u.zD)(n),C=(0,b.$)(null==w?void 0:w.base,y),z=!!v,E="dot"===e.variant,{focusProps:N,isFocusVisible:A}=(0,o.o)(),j=(0,h.useMemo)(()=>"string"==typeof d&&(null==d?void 0:d.length)===1,[d]),P=(0,h.useMemo)(()=>!!p||!!f,[p,f]),I=(0,h.useMemo)(()=>!!g||z,[g,z]),M=(0,h.useMemo)(()=>c({...r,hasStartContent:P,hasEndContent:I,isOneChar:j,isCloseable:z,isCloseButtonFocusVisible:A}),[(0,m.t6)(r),A,P,I,j,z]),{pressProps:D}=(0,l.d)({isDisabled:!!(null==e?void 0:e.isDisabled),onPress:v}),B=e=>(0,h.isValidElement)(e)?(0,h.cloneElement)(e,{className:(0,b.$)("max-h-[80%]",e.props.className)}):null;return{Component:i||"div",children:d,slots:M,classNames:w,isDot:E,isCloseable:z,startContent:((0,h.isValidElement)(p)?(0,h.cloneElement)(p,{className:M.avatar({class:null==w?void 0:w.avatar})}):null)||B(f),endContent:B(g),getCloseButtonProps:()=>({role:"button",tabIndex:0,className:M.closeButton({class:null==w?void 0:w.closeButton}),"aria-label":"close chip",...(0,t.v)(D,N)}),getChipProps:()=>({ref:x,className:M.base({class:C}),...k})}}({...e,ref:a}),C=(0,h.useMemo)(()=>g&&!w?(0,f.jsx)("span",{className:i.dot({class:null==d?void 0:d.dot})}):w,[i,w,g]),z=(0,h.useMemo)(()=>v?(0,f.jsx)("span",{...k(),children:y||(0,f.jsx)(p.o,{})}):y,[y,v,k]);return(0,f.jsxs)(r,{...x(),children:[C,(0,f.jsx)("span",{className:i.content({class:null==d?void 0:d.content}),children:n}),z]})});g.displayName="NextUI.Chip";var v=g}}]);