(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[783],{1701:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,dynamic:()=>d});var s=r(95155),n=r(12115),a=r(35695),o=r(59434),l=r(82842),c=r(28920),i=r(66146);let d="force-dynamic";function u(){let e=(0,a.useRouter)(),t=(0,a.useSearchParams)(),[r,d]=(0,l.lT)(),[u,h]=(0,n.useState)("loading"),[m,x]=(0,n.useState)("");return(0,n.useEffect)(()=>{(async()=>{try{let r=t.get("code");if(t.get("error")){h("error"),x("تم إلغاء تسجيل الدخول بواسطة Google");return}if(!r){h("error"),x("لم يتم الحصول على رمز التفويض من Google");return}let s=await (0,o.G)("/auth/google/",{code:r},"POST");if(s.ok){let t=await s.json();d("access",t.access_token,{path:"/"}),d("refresh",t.refresh_token,{path:"/"}),h("success"),setTimeout(()=>{e.push("/")},1500)}else{let e=await s.json();h("error"),x(e.detail||"فشل في تسجيل الدخول بواسطة Google")}}catch(e){console.error("Google OAuth callback error:",e),h("error"),x("حدث خطأ أثناء تسجيل الدخول")}})()},[t,d,e]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center",children:["loading"===u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.o,{size:"lg",color:"primary"}),(0,s.jsx)("h2",{className:"mt-4 text-xl font-semibold text-gray-900",children:"جاري تسجيل الدخول..."}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"يرجى الانتظار بينما نقوم بتسجيل دخولك بواسطة Google"})]}),"success"===u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h2",{className:"mt-4 text-xl font-semibold text-green-900",children:"تم تسجيل الدخول بنجاح!"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"سيتم توجيهك إلى الصفحة الرئيسية..."})]}),"error"===u&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsx)("h2",{className:"mt-4 text-xl font-semibold text-red-900",children:"فشل تسجيل الدخول"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:m}),(0,s.jsx)(i.T,{onClick:()=>e.push("/auth"),className:"mt-4 w-full",color:"primary",children:"العودة إلى صفحة تسجيل الدخول"})]})]})})}function h(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center",children:[(0,s.jsx)(c.o,{size:"lg",color:"primary"}),(0,s.jsx)("h2",{className:"mt-4 text-xl font-semibold text-gray-900",children:"جاري تحميل..."})]})}),children:(0,s.jsx)(u,{})})}},13031:(e,t,r)=>{Promise.resolve().then(r.bind(r,1701))},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},59434:(e,t,r)=>{"use strict";r.d(t,{G:()=>o,cn:()=>a});var s=r(52596),n=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}async function o(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",s=arguments.length>3?arguments[3]:void 0,n={"Content-Type":"application/json"};s&&(n.Authorization="Bearer ".concat(s));let a={method:r,headers:n,next:{revalidate:60}};t&&"GET"!==r&&(a.body=JSON.stringify(t));try{let t="".concat("http://localhost:8000").concat(e);console.log("Fetching: ".concat(t));let r=await fetch(t,a);return r.ok||console.warn("API request failed: ".concat(t," returned status ").concat(r.status)),r}catch(e){throw console.error("API request failed:",e),e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[146,688,842,441,684,358],()=>t(13031)),_N_E=e.O()}]);