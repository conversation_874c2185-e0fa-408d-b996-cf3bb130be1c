// Final Google OAuth Integration Test
// Comprehensive test of the complete OAuth flow

const API_BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

async function testCompleteOAuthIntegration() {
    console.log('🔍 Final Google OAuth Integration Test');
    console.log('=====================================\n');
    
    const results = {
        backendUrl: false,
        backendCallback: false,
        frontendAuth: false,
        frontendCallback: false,
        configuration: false
    };
    
    // Test 1: Backend OAuth URL endpoint
    console.log('1. Testing Backend OAuth URL Endpoint...');
    try {
        const response = await fetch(`${API_BASE_URL}/auth/google/url/`);
        const data = await response.json();
        
        if (response.ok && data.url && data.url.includes('accounts.google.com')) {
            console.log('✅ Backend OAuth URL endpoint working');
            results.backendUrl = true;
            
            // Validate OAuth URL parameters
            const url = new URL(data.url);
            const hasClientId = url.searchParams.has('client_id');
            const hasRedirectUri = url.searchParams.has('redirect_uri');
            const hasScope = url.searchParams.has('scope');
            
            if (hasClientId && hasRedirectUri && hasScope) {
                console.log('✅ OAuth URL contains all required parameters');
                results.configuration = true;
            } else {
                console.log('❌ OAuth URL missing required parameters');
            }
        } else {
            console.log('❌ Backend OAuth URL endpoint failed');
        }
    } catch (error) {
        console.log('❌ Backend OAuth URL endpoint error:', error.message);
    }
    
    // Test 2: Backend OAuth callback endpoint
    console.log('\n2. Testing Backend OAuth Callback Endpoint...');
    try {
        const response = await fetch(`${API_BASE_URL}/auth/google/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code: 'test_invalid_code' })
        });
        
        // Should return 400 for invalid code, which means endpoint is working
        if (response.status === 400) {
            console.log('✅ Backend OAuth callback endpoint responding correctly');
            results.backendCallback = true;
        } else {
            console.log(`⚠️  Backend OAuth callback unexpected status: ${response.status}`);
        }
    } catch (error) {
        console.log('❌ Backend OAuth callback endpoint error:', error.message);
    }
    
    // Test 3: Frontend auth page
    console.log('\n3. Testing Frontend Auth Page...');
    try {
        const response = await fetch(`${FRONTEND_URL}/auth`);
        if (response.ok) {
            console.log('✅ Frontend auth page accessible');
            results.frontendAuth = true;
        } else {
            console.log('❌ Frontend auth page not accessible');
        }
    } catch (error) {
        console.log('❌ Frontend auth page error:', error.message);
    }
    
    // Test 4: Frontend callback page
    console.log('\n4. Testing Frontend Callback Page...');
    try {
        const response = await fetch(`${FRONTEND_URL}/googlecallback`);
        if (response.ok) {
            console.log('✅ Frontend callback page accessible');
            results.frontendCallback = true;
        } else {
            console.log('❌ Frontend callback page not accessible');
        }
    } catch (error) {
        console.log('❌ Frontend callback page error:', error.message);
    }
    
    // Test 5: Environment configuration check
    console.log('\n5. Checking Environment Configuration...');
    try {
        const response = await fetch(`${API_BASE_URL}/auth/google/url/`);
        const data = await response.json();
        
        if (data.url) {
            const clientIdMatch = data.url.match(/client_id=([^&]+)/);
            const redirectMatch = data.url.match(/redirect_uri=([^&]+)/);
            
            if (clientIdMatch && clientIdMatch[1] !== 'None' && clientIdMatch[1] !== '') {
                console.log('✅ Google Client ID configured');
            } else {
                console.log('❌ Google Client ID not configured');
                results.configuration = false;
            }
            
            if (redirectMatch) {
                const redirectUri = decodeURIComponent(redirectMatch[1]);
                if (redirectUri === 'http://localhost:3000/googlecallback') {
                    console.log('✅ Redirect URI correctly configured');
                } else {
                    console.log(`⚠️  Redirect URI: ${redirectUri}`);
                }
            }
        }
    } catch (error) {
        console.log('❌ Environment configuration check error:', error.message);
    }
    
    // Summary
    console.log('\n📊 Integration Test Results:');
    console.log('============================');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅' : '❌';
        const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        console.log(`${status} ${testName}`);
    });
    
    console.log(`\n📈 Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 ALL TESTS PASSED! Google OAuth integration is ready!');
        console.log('\n✅ Integration Status: FULLY FUNCTIONAL');
        console.log('✅ Backend Endpoints: Working');
        console.log('✅ Frontend Pages: Working');
        console.log('✅ Configuration: Complete');
        
        console.log('\n🚀 Ready for Manual Testing:');
        console.log('============================');
        console.log('1. Open: http://localhost:3000/auth');
        console.log('2. Click "تسجيل الدخول بواسطة Google" button');
        console.log('3. Complete Google OAuth flow');
        console.log('4. Verify successful login and redirect');
        
    } else {
        console.log('\n⚠️  Some components need attention:');
        Object.entries(results).forEach(([test, passed]) => {
            if (!passed) {
                console.log(`❌ ${test}: Needs fixing`);
            }
        });
    }
    
    return results;
}

// Run the comprehensive test
testCompleteOAuthIntegration().catch(console.error);
