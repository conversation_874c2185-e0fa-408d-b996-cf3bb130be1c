{"kind": "FETCH", "data": {"headers": {"allow": "GET, HEAD, OPTIONS", "content-length": "368", "content-type": "application/json", "cross-origin-opener-policy": "same-origin", "referrer-policy": "same-origin", "server": "daphne", "vary": "Accept, origin", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJjb3VudCI6MSwibmV4dCI6bnVsbCwicHJldmlvdXMiOm51bGwsInJlc3VsdHMiOlt7ImlkIjozLCJlbWVyZ2VuY3lfdHlwZSI6IkQiLCJjcmVhdGVkX2F0IjoiMjAyNS0wNS0yNVQyMDoxNDoyMi4xMDc1NDdaIiwibG9jYXRpb24iOiLYqNmK2Kog2YTYrdmFIiwiaW1hZ2UiOiJodHRwOi8vbG9jYWxob3N0OjgwMDAvbWVkaWEvZW1lcmdlbmN5L2ltYWdlcy9wbmd0cmVlLWRlbW9saXNoZWQtaG91c2Utd2luZG93LXRlYXItZG93bi1ob3VzZS1kZW1vbGl0aW9uLXBob3RvLWltYWdlXzE1MTIyODYwLmpwZyIsImltYWdlX2NvdW50IjoxLCJ1c2VyX2ZpcnN0X25hbWUiOiJNb250YXNlckJhbGxoIiwidXNlcl9sYXN0X25hbWUiOiJIYXJmb3VzaCJ9XX0=", "status": 200, "url": "http://localhost:8000/emergency/?emergency_type=D"}, "revalidate": 60, "tags": []}