"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/animated-modal";
import { Card, CardBody, ScrollShadow, Image, Chip, Skeleton } from "@nextui-org/react";
import { ChevronRight, Info, Camera, Clock, MapPin, AlertTriangle, Heart, Shield, ImageIcon } from "lucide-react";
import Link from "next/link";
import { AlertItemDetails } from "../AlertItemDetails/page";
import { useState } from "react";
import NextImage from "next/image";

interface AlertRequest {
  id: string;
  user_first_name: string;
  user_last_name: string;
  image: string;
  image_count: number;
  created_at: string;
  location: string;
  emergency_type: string;
}

type Props = {
  data: AlertRequest[];
  heading: string;
};

// Utility functions for emergency types
const getEmergencyTypeLabel = (type: string): string => {
  switch (type) {
    case "O": return "طلب مساعدة"
    case "M": return "طبية"
    case "D": return "خطر"
    default: return "غير محدد"
  }
}

const getEmergencyTypeColor = (type: string) => {
  switch (type) {
    case "O": return "primary"
    case "M": return "success"
    case "D": return "danger"
    default: return "default"
  }
}

const getEmergencyTypeIcon = (type: string) => {
  switch (type) {
    case "O": return Heart
    case "M": return Shield
    case "D": return AlertTriangle
    default: return AlertTriangle
  }
}

const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return "الآن"
  if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `منذ ${diffInHours} ساعة`

  const diffInDays = Math.floor(diffInHours / 24)
  return `منذ ${diffInDays} يوم`
}

// Enhanced Image Component with loading states and better performance
function EmergencyImage({
  src,
  alt,
  hasMultipleImages,
  imageCount = 0
}: {
  src: string;
  alt: string;
  hasMultipleImages?: boolean;
  imageCount?: number;
}) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  return (
    <div className="relative w-full h-48 overflow-hidden rounded-t-lg bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Enhanced Loading Skeleton */}
      {isLoading && (
        <div className="absolute inset-0 w-full h-full">
          <Skeleton className="w-full h-full rounded-t-lg">
            <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse" />
          </Skeleton>
        </div>
      )}

      {/* Optimized Image with Next.js Image component for better performance */}
      <div className="relative w-full h-full">
        <Image
          src={src}
          alt={alt}
          className={`object-cover w-full h-full transition-all duration-300 hover:scale-105 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          fallbackSrc="/placeholder.svg?height=192&width=320"
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setIsLoading(false)
            setHasError(true)
          }}
          loading="lazy"
        />
      </div>

      {/* Enhanced Multiple Images Indicator with count */}
      {hasMultipleImages && !hasError && imageCount > 1 && (
        <div className="absolute top-3 right-3 bg-black/80 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs flex items-center gap-1.5 shadow-lg">
          <Camera className="w-3.5 h-3.5" />
          <span className="font-medium">{imageCount} صور</span>
        </div>
      )}

      {/* Enhanced Image Overlay on Hover */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 hover:opacity-100 transition-all duration-300 flex items-center justify-center">
        <div className="bg-white/90 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-lg text-sm font-medium shadow-lg transform translate-y-2 hover:translate-y-0 transition-transform duration-300">
          <div className="flex items-center gap-2">
            <ImageIcon className="w-4 h-4" />
            <span>انقر لعرض التفاصيل</span>
          </div>
        </div>
      </div>

      {/* Error State Enhancement */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center text-gray-500">
            <ImageIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-xs">فشل تحميل الصورة</p>
          </div>
        </div>
      )}
    </div>
  )
}

export function AlertSection({ data = [], heading }: Props) {
  // Ensure data is an array
  const safeData = Array.isArray(data) ? data : [];

  return (
    <div className="w-full max-w-[1200px] mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Link href="/previous" className="text-blue-500 hover:text-blue-600">
            <ChevronRight className="w-5 h-5" />
          </Link>
          <h2 className="text-xl font-bold">{heading}</h2>
        </div>
      </div>

      {safeData.length === 0 ? (
        <div className="flex flex-col items-center justify-center w-full h-48 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <AlertTriangle className="w-12 h-12 text-gray-400 mb-2" />
          <p className="text-gray-500 text-lg font-medium">لا توجد تنبيهات حالياً</p>
          <p className="text-gray-400 text-sm">سيتم عرض الطلبات الجديدة هنا</p>
        </div>
      ) : (
        <ScrollShadow
          orientation="horizontal"
          className="flex gap-4 sm:gap-6 w-full overflow-x-auto pb-4 px-1"
        >
          {safeData.map((alert) => {
            const EmergencyIcon = getEmergencyTypeIcon(alert.emergency_type)

            return (
              <Card
                key={alert.id}
                className="flex-none w-[280px] sm:w-[320px] border border-gray-200 emergency-card-hover hover:border-gray-300 hover:shadow-lg transition-all duration-300 bg-white rounded-lg overflow-hidden"
              >
                <CardBody className="p-0">
                  {/* Enhanced Image Section */}
                  {alert.image ? (
                    <EmergencyImage
                      src={alert.image}
                      alt={`صورة طوارئ ${getEmergencyTypeLabel(alert.emergency_type)} من ${alert.user_first_name} ${alert.user_last_name} في ${alert.location}`}
                      hasMultipleImages={alert.image_count > 1}
                      imageCount={alert.image_count}
                    />
                  ) : (
                    <div className="w-full h-48 bg-gradient-to-br from-gray-50 to-gray-150 rounded-t-lg flex items-center justify-center border-b border-gray-100">
                      <div className="text-center p-6">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                          <EmergencyIcon className="w-8 h-8 text-gray-400" />
                        </div>
                        <p className="text-gray-500 text-sm font-medium">لا توجد صورة مرفقة</p>
                        <p className="text-gray-400 text-xs mt-1">
                          {getEmergencyTypeLabel(alert.emergency_type)}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Content Section */}
                  <div className="p-4 space-y-3">
                    {/* Emergency Type Badge */}
                    <div className="flex items-center justify-between">
                      <Chip
                        color={getEmergencyTypeColor(alert.emergency_type) as any}
                        size="sm"
                        variant="flat"
                        startContent={<EmergencyIcon className="w-3 h-3" />}
                      >
                        {getEmergencyTypeLabel(alert.emergency_type)}
                      </Chip>

                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="w-3 h-3" />
                        <span>{formatTimeAgo(alert.created_at)}</span>
                      </div>
                    </div>

                    {/* User Info */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 text-sm font-medium">
                            {alert.user_first_name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-semibold text-sm text-gray-900">
                            {alert.user_first_name} {alert.user_last_name}
                          </h3>
                        </div>
                      </div>

                      <div className="flex items-start gap-2">
                        <MapPin className="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" />
                        <p className="text-xs text-gray-600 line-clamp-2">{alert.location}</p>
                      </div>
                    </div>

                    {/* Action Button */}
                    <div className="pt-2 border-t border-gray-100">
                      <Modal key={`modal-${alert.id}`}>
                        <ModalTrigger className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                          <Info className="w-4 h-4" />
                          عرض التفاصيل
                        </ModalTrigger>
                        <ModalBody>
                          <ModalContent>
                            <AlertItemDetails id={alert.id} />
                          </ModalContent>
                        </ModalBody>
                      </Modal>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )
          })}
        </ScrollShadow>
      )}
    </div>
  );
}
