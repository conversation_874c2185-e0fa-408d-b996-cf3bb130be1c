"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { fetcher } from "@/lib/utils"
import { useCookies } from "react-cookie"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@nextui-org/react"

// Force dynamic rendering to prevent prerendering issues with useSearchParams
export const dynamic = 'force-dynamic'

function GoogleCallbackContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [cookies, setCookie] = useCookies()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [errorMessage, setErrorMessage] = useState('')

  useEffect(() => {
    const handleGoogleCallback = async () => {
      try {
        // Get the authorization code from URL parameters
        const code = searchParams.get('code')
        const error = searchParams.get('error')

        if (error) {
          setStatus('error')
          setErrorMessage('تم إلغاء تسجيل الدخول بواسطة Google')
          return
        }

        if (!code) {
          setStatus('error')
          setErrorMessage('لم يتم الحصول على رمز التفويض من Google')
          return
        }

        // Send the authorization code to the backend
        const response = await fetcher("/auth/google/", { code }, "POST")

        if (response.ok) {
          const data = await response.json()

          // Set the JWT tokens in cookies
          setCookie("access", data.access_token, { path: "/" })
          setCookie("refresh", data.refresh_token, { path: "/" })

          setStatus('success')

          // Redirect to home page after successful authentication
          setTimeout(() => {
            router.push("/")
          }, 1500)
        } else {
          const errorData = await response.json()
          setStatus('error')
          setErrorMessage(errorData.detail || 'فشل في تسجيل الدخول بواسطة Google')
        }
      } catch (error) {
        console.error('Google OAuth callback error:', error)
        setStatus('error')
        setErrorMessage('حدث خطأ أثناء تسجيل الدخول')
      }
    }

    handleGoogleCallback()
  }, [searchParams, setCookie, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
        {status === 'loading' && (
          <>
            <Spinner size="lg" color="primary" />
            <h2 className="mt-4 text-xl font-semibold text-gray-900">
              جاري تسجيل الدخول...
            </h2>
            <p className="mt-2 text-gray-600">
              يرجى الانتظار بينما نقوم بتسجيل دخولك بواسطة Google
            </p>
          </>
        )}

        {status === 'success' && (
          <>
            <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="mt-4 text-xl font-semibold text-green-900">
              تم تسجيل الدخول بنجاح!
            </h2>
            <p className="mt-2 text-gray-600">
              سيتم توجيهك إلى الصفحة الرئيسية...
            </p>
          </>
        )}

        {status === 'error' && (
          <>
            <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="mt-4 text-xl font-semibold text-red-900">
              فشل تسجيل الدخول
            </h2>
            <p className="mt-2 text-gray-600">
              {errorMessage}
            </p>
            <Button
              onClick={() => router.push('/auth')}
              className="mt-4 w-full"
              color="primary"
            >
              العودة إلى صفحة تسجيل الدخول
            </Button>
          </>
        )}
      </div>
    </div>
  )
}

export default function GoogleCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <Spinner size="lg" color="primary" />
          <h2 className="mt-4 text-xl font-semibold text-gray-900">
            جاري تحميل...
          </h2>
        </div>
      </div>
    }>
      <GoogleCallbackContent />
    </Suspense>
  )
}
