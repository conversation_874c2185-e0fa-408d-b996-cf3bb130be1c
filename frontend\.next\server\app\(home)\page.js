(()=>{var e={};e.id=813,e.ids=[813],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17977:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20187:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(65239),s=r(48088),l=r(88170),n=r.n(l),o=r(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let d={children:["",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17977)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(home)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41566:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var a=(0,r(72926).tv)({base:[],variants:{orientation:{vertical:["overflow-y-auto","data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],horizontal:["overflow-x-auto","data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}})},54979:(e,t,r)=>{Promise.resolve().then(r.bind(r,85022))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78955:(e,t,r)=>{Promise.resolve().then(r.bind(r,17977))},79293:(e,t,r)=>{"use strict";r.d(t,{H:()=>c});var a=r(26109),s=r(41566),l=r(54514),n=r(82432),o=r(43210),i=r(60687),d=(0,a.Rf)((e,t)=>{let{Component:r,children:d,getBaseProps:c}=function(e){var t;let[r,i]=(0,a.rE)(e,s.Q.variantKeys),{ref:d,as:c,children:m,className:u,style:h,size:p=40,offset:x=0,visibility:f="auto",isEnabled:g=!0,onVisibilityChange:v,...b}=r,j=(0,l.zD)(d);!function(e={}){let{domRef:t,isEnabled:r=!0,overflowCheck:a="vertical",visibility:s="auto",offset:l=0,onVisibilityChange:i,updateDeps:d=[]}=e,c=(0,o.useRef)(s);(0,o.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!r)return;let o=(t,r,a,l,o)=>{if("auto"===s){let t=`${l}${(0,n.ZH)(o)}Scroll`;r&&a?(e.dataset[t]="true",e.removeAttribute(`data-${l}-scroll`),e.removeAttribute(`data-${o}-scroll`)):(e.dataset[`${l}Scroll`]=r.toString(),e.dataset[`${o}Scroll`]=a.toString(),e.removeAttribute(`data-${l}-${o}-scroll`))}else{let e=r&&a?"both":r?l:a?o:"none";e!==c.current&&(null==i||i(e),c.current=e)}},d=()=>{for(let{type:t,prefix:r,suffix:s}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(a===t||"both"===a){let a="vertical"===t?e.scrollTop>l:e.scrollLeft>l,n="vertical"===t?e.scrollTop+e.clientHeight+l<e.scrollHeight:e.scrollLeft+e.clientWidth+l<e.scrollWidth;o(t,a,n,r,s)}},m=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute(`data-${t}-scroll`)})};return d(),e.addEventListener("scroll",d),"auto"!==s&&(m(),"both"===s?(e.dataset.topBottomScroll=String("vertical"===a),e.dataset.leftRightScroll=String("horizontal"===a)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset[`${t}Scroll`]=String(s===t)}))),()=>{e.removeEventListener("scroll",d),m()}},[...d,r,s,a,i,t])}({domRef:j,offset:x,visibility:f,isEnabled:g,onVisibilityChange:v,updateDeps:[m],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let y=(0,o.useMemo)(()=>(0,s.Q)({...i,className:u}),[(0,n.t6)(i),u]);return{Component:c||"div",styles:y,domRef:j,children:m,getBaseProps:(t={})=>{var r;return{ref:j,className:y,"data-orientation":null!=(r=e.orientation)?r:"vertical",style:{"--scroll-shadow-size":`${p}px`,...h,...t.style},...b,...t}}}}({...e,ref:t});return(0,i.jsx)(r,{...c(),children:d})});d.displayName="NextUI.ScrollShadow";var c=d},79551:e=>{"use strict";e.exports=require("url")},85022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U});var a=r(60687),s=r(71356),l=r(26109),n=(0,r(72926).tv)({slots:{base:["group","relative","overflow-hidden","bg-content3 dark:bg-content2","pointer-events-none","before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-[shimmer_2s_infinite]","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2","data-[loaded=true]:pointer-events-auto","data-[loaded=true]:overflow-visible","data-[loaded=true]:!bg-transparent","data-[loaded=true]:before:opacity-0 data-[loaded=true]:before:-z-10 data-[loaded=true]:before:animate-none","data-[loaded=true]:after:opacity-0"],content:["opacity-0","group-data-[loaded=true]:opacity-100"]},variants:{disableAnimation:{true:{base:"before:animate-none before:transition-none after:transition-none",content:"transition-none"},false:{base:"transition-background !duration-300",content:"transition-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{}}),o=r(82432),i=r(16060),d=r(1172),c=r(43210),m=r(55150),u=(0,l.Rf)((e,t)=>{let{Component:r,children:s,getSkeletonProps:u,getContentProps:h}=function(e){var t,r;let a=(0,m.o)(),[s,u]=(0,l.rE)(e,n.variantKeys),{as:h,children:p,isLoaded:x=!1,className:f,classNames:g,...v}=s,b=null!=(r=null!=(t=e.disableAnimation)?t:null==a?void 0:a.disableAnimation)&&r,j=(0,c.useMemo)(()=>n({...u,disableAnimation:b}),[(0,o.t6)(u),b,p]),y=(0,i.$)(null==g?void 0:g.base,f);return{Component:h||"div",children:p,slots:j,classNames:g,getSkeletonProps:(e={})=>({"data-loaded":(0,d.sE)(x),className:j.base({class:(0,i.$)(y,null==e?void 0:e.className)}),...v}),getContentProps:(e={})=>({className:j.content({class:(0,i.$)(null==g?void 0:g.content,null==e?void 0:e.className)})})}}({...e});return(0,a.jsx)(r,{ref:t,...u(),children:(0,a.jsx)("div",{...h(),children:s})})});u.displayName="NextUI.Skeleton";var h=r(97939),p=r(79293),x=r(63257),f=r(86760),g=r(72199),v=r(67760),b=r(99891),j=r(43649),y=r(62688);let N=(0,y.A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),w=(0,y.A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),_=(0,y.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var k=r(48730),S=r(97992);let A=(0,y.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var z=r(85814),M=r.n(z),P=r(52326);let $=e=>{switch(e){case"O":return"طلب مساعدة";case"M":return"طبية";case"D":return"خطر";default:return"غير محدد"}},E=e=>{switch(e){case"O":return"primary";case"M":return"success";case"D":return"danger";default:return"default"}},G=e=>{switch(e){case"O":return v.A;case"M":return b.A;default:return j.A}},C=e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/6e4);if(r<1)return"الآن";if(r<60)return`منذ ${r} دقيقة`;let a=Math.floor(r/60);if(a<24)return`منذ ${a} ساعة`;let s=Math.floor(a/24);return`منذ ${s} يوم`};function q({src:e,alt:t,hasMultipleImages:r,imageCount:s=0}){let[l,n]=(0,c.useState)(!0),[o,i]=(0,c.useState)(!1);return(0,a.jsxs)("div",{className:"relative w-full h-48 overflow-hidden rounded-t-lg bg-gradient-to-br from-gray-50 to-gray-100",children:[l&&(0,a.jsx)("div",{className:"absolute inset-0 w-full h-full",children:(0,a.jsx)(u,{className:"w-full h-full rounded-t-lg",children:(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"})})}),(0,a.jsx)("div",{className:"relative w-full h-full",children:(0,a.jsx)(h.W,{src:e,alt:t,className:`object-cover w-full h-full transition-all duration-300 hover:scale-105 ${l?"opacity-0":"opacity-100"}`,fallbackSrc:"/placeholder.svg?height=192&width=320",onLoad:()=>n(!1),onError:()=>{n(!1),i(!0)},loading:"lazy"})}),r&&!o&&s>1&&(0,a.jsxs)("div",{className:"absolute top-3 right-3 bg-black/80 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-xs flex items-center gap-1.5 shadow-lg",children:[(0,a.jsx)(N,{className:"w-3.5 h-3.5"}),(0,a.jsxs)("span",{className:"font-medium",children:[s," صور"]})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 hover:opacity-100 transition-all duration-300 flex items-center justify-center",children:(0,a.jsx)("div",{className:"bg-white/90 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-lg text-sm font-medium shadow-lg transform translate-y-2 hover:translate-y-0 transition-transform duration-300",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"انقر لعرض التفاصيل"})]})})}),o&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-100 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(w,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{className:"text-xs",children:"فشل تحميل الصورة"})]})})]})}function D({data:e=[],heading:t}){let r=Array.isArray(e)?e:[];return(0,a.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(M(),{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,a.jsx)(_,{className:"w-5 h-5"})}),(0,a.jsx)("h2",{className:"text-xl font-bold",children:t})]})}),0===r.length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-48 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300",children:[(0,a.jsx)(j.A,{className:"w-12 h-12 text-gray-400 mb-2"}),(0,a.jsx)("p",{className:"text-gray-500 text-lg font-medium",children:"لا توجد تنبيهات حالياً"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"سيتم عرض الطلبات الجديدة هنا"})]}):(0,a.jsx)(p.H,{orientation:"horizontal",className:"flex gap-4 sm:gap-6 w-full overflow-x-auto pb-4 px-1",children:r.map(e=>{let t=G(e.emergency_type);return(0,a.jsx)(x.Z,{className:"flex-none w-[280px] sm:w-[320px] border border-gray-200 emergency-card-hover hover:border-gray-300 hover:shadow-lg transition-all duration-300 bg-white rounded-lg overflow-hidden",children:(0,a.jsxs)(f.U,{className:"p-0",children:[e.image?(0,a.jsx)(q,{src:e.image,alt:`صورة طوارئ ${$(e.emergency_type)} من ${e.user_first_name} ${e.user_last_name} في ${e.location}`,hasMultipleImages:e.image_count>1,imageCount:e.image_count}):(0,a.jsx)("div",{className:"w-full h-48 bg-gradient-to-br from-gray-50 to-gray-150 rounded-t-lg flex items-center justify-center border-b border-gray-100",children:(0,a.jsxs)("div",{className:"text-center p-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(t,{className:"w-8 h-8 text-gray-400"})}),(0,a.jsx)("p",{className:"text-gray-500 text-sm font-medium",children:"لا توجد صورة مرفقة"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs mt-1",children:$(e.emergency_type)})]})}),(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(g.R,{color:E(e.emergency_type),size:"sm",variant:"flat",startContent:(0,a.jsx)(t,{className:"w-3 h-3"}),children:$(e.emergency_type)}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,a.jsx)(k.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:C(e.created_at)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 text-sm font-medium",children:e.user_first_name.charAt(0)})}),(0,a.jsx)("div",{children:(0,a.jsxs)("h3",{className:"font-semibold text-sm text-gray-900",children:[e.user_first_name," ",e.user_last_name]})})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(S.A,{className:"w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 line-clamp-2",children:e.location})]})]}),(0,a.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,a.jsxs)(s.aF,{children:[(0,a.jsxs)(s.g6,{className:"w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2",children:[(0,a.jsx)(A,{className:"w-4 h-4"}),"عرض التفاصيل"]}),(0,a.jsx)(s.cw,{children:(0,a.jsx)(s.$m,{children:(0,a.jsx)(P.A,{id:e.id})})})]},`modal-${e.id}`)})]})]})},e.id)})})]})}let I=(0,y.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),L=(0,y.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);function O({}){return(0,a.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(x.Z,{children:(0,a.jsxs)(f.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,a.jsx)(I,{className:"w-6 h-6 text-blue-500"}),(0,a.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,a.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,a.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,a.jsx)(x.Z,{children:(0,a.jsxs)(f.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,a.jsx)(L,{className:"w-6 h-6 text-blue-500"}),(0,a.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,a.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,a.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,a.jsx)(x.Z,{children:(0,a.jsxs)(f.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,a.jsx)(S.A,{className:"w-6 h-6 text-blue-500"}),(0,a.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,a.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,a.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}r(4780);var R=r(36424);let H=(0,y.A)("Siren",[["path",{d:"M7 18v-6a5 5 0 1 1 10 0v6",key:"pcx96s"}],["path",{d:"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z",key:"1b4s83"}],["path",{d:"M21 12h1",key:"jtio3y"}],["path",{d:"M18.5 4.5 18 5",key:"g5sp9y"}],["path",{d:"M2 12h1",key:"1uaihz"}],["path",{d:"M12 2v1",key:"11qlp1"}],["path",{d:"m4.929 4.929.707.707",key:"1i51kw"}],["path",{d:"M12 12v6",key:"3ahymv"}]]),T=(0,y.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function U(e){new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString(),new Date().toISOString();let[t,r]=(0,c.useState)({results:[]}),[s,l]=(0,c.useState)({results:[]}),[n,o]=(0,c.useState)({results:[]}),[i,d]=(0,c.useState)(!0),[m,u]=(0,c.useState)(null);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]",children:(0,a.jsxs)("div",{id:"send-emergency",className:"max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center",children:[(0,a.jsx)(H,{className:"absolute top-3 left-3"}),(0,a.jsx)("h3",{children:"أرسل تنبيهًا للطوارئ"}),(0,a.jsx)("p",{children:"حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن."}),(0,a.jsx)(R.T,{as:M(),href:"/add-application",endContent:(0,a.jsx)(T,{}),className:"mx-auto",color:"danger",children:"أرسل إشعار للطوارئ"})]})}),(0,a.jsxs)("section",{id:"recieved-emergency",className:"mt-[65vh]",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center",children:"إشعارات الطوارئ المستلمة"}),i?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"جاري تحميل الإشعارات..."})]}):m?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-red-500 mb-4",children:"حدث خطأ في تحميل البيانات"}),(0,a.jsx)("p",{className:"text-gray-600",children:"سيتم عرض البيانات التجريبية"})]}):null,(0,a.jsx)(D,{data:t.results,heading:"طلبات التنبيه حول الإغاثة"}),(0,a.jsx)(D,{data:s.results,heading:"طلبات التنبيه حول الصحة"}),(0,a.jsx)(D,{data:n.results,heading:"طلبات التنبيه حول الخطر"})]}),(0,a.jsx)(O,{})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,172,443,416,966,513,317,876,159],()=>r(20187));module.exports=a})();