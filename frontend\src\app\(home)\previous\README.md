# Previous Emergency Notifications Page

## Overview

The `/previous` page displays all emergency notifications that have been submitted to the system. This page provides a comprehensive view of historical emergency data with filtering, pagination, and detailed view capabilities.

## Features

### 🔍 **Filtering**
- **All Notifications**: View all emergency notifications regardless of type
- **Offer Help (طلب مساعدة)**: Filter for assistance requests
- **Medical (طبية)**: Filter for medical emergencies
- **Danger (خطر)**: Filter for danger alerts

### 📄 **Pagination**
- Displays 12 notifications per page
- NextUI Pagination component with Arabic RTL support
- Smooth navigation between pages

### 📱 **Responsive Design**
- Grid layout that adapts to different screen sizes:
  - Mobile: 1 column
  - Tablet: 2 columns
  - Desktop: 3 columns
  - Large screens: 4 columns

### 🎨 **UI Components**
- **Emergency Cards**: Each notification displayed in a clean card format
- **Type Badges**: Color-coded chips indicating emergency type
- **Modal Details**: Click any card to view full details
- **Loading States**: Spinner and skeleton loading states
- **Error Handling**: User-friendly error messages with retry options

## Technical Implementation

### API Integration
```typescript
// Fetches paginated emergency notifications
GET /emergency/?page={page}&page_size=12&emergency_type={type}
```

### State Management
- `notifications`: Array of emergency notification objects
- `loading`: Boolean for loading state
- `error`: String for error messages
- `currentPage`: Current pagination page
- `selectedType`: Currently selected filter type
- `totalPages`: Total number of pages
- `totalCount`: Total number of notifications

### Data Structure
```typescript
interface EmergencyNotification {
  id: number;
  emergency_type: string; // 'O' | 'M' | 'D'
  description: string;
  location: string;
  created_at: string;
  user_first_name: string;
  user_last_name: string;
  images?: { id: number; src: string }[];
}
```

## Navigation Integration

The page is accessible through:
1. **Header Navigation**: "الإشعارات السابقة" link in the main navigation
2. **Direct URL**: `/previous`

## Styling

### Arabic RTL Support
- Full right-to-left layout support
- Arabic date formatting
- Proper text alignment and spacing

### Color Coding
- **Offer Help**: Primary blue color
- **Medical**: Success green color  
- **Danger**: Danger red color

### Responsive Breakpoints
- `grid-cols-1`: Mobile (default)
- `md:grid-cols-2`: Tablet (768px+)
- `lg:grid-cols-3`: Desktop (1024px+)
- `xl:grid-cols-4`: Large screens (1280px+)

## Error Handling

### Network Errors
- Displays user-friendly error message in Arabic
- Provides retry button to refetch data
- Graceful fallback to empty state

### Empty States
- Shows appropriate message when no notifications exist
- Different messages for filtered vs. unfiltered views

## Performance Optimizations

### Pagination
- Limits API requests to 12 items per page
- Reduces initial load time and memory usage

### Caching
- Uses Next.js built-in caching with 60-second revalidation
- Optimizes repeated API calls

### Loading States
- Immediate loading feedback for better UX
- Skeleton loading for smooth transitions

## Accessibility

### Keyboard Navigation
- Full keyboard support for all interactive elements
- Proper tab order and focus management

### Screen Readers
- Semantic HTML structure
- Proper ARIA labels and descriptions
- Arabic language support

## Testing

### Unit Tests
Located in `__tests__/page.test.tsx`:
- Loading state rendering
- Successful data display
- Error state handling
- Empty state rendering
- Emergency type filtering
- Pagination functionality

### Manual Testing Checklist
- [ ] Page loads without errors
- [ ] Navigation link works correctly
- [ ] Filtering by emergency type works
- [ ] Pagination controls function properly
- [ ] Modal details open correctly
- [ ] Responsive design works on all screen sizes
- [ ] Error states display properly
- [ ] Loading states show correctly
- [ ] Arabic text displays correctly (RTL)

## Future Enhancements

### Potential Improvements
1. **Search Functionality**: Add text search within notifications
2. **Date Range Filtering**: Filter by creation date
3. **Export Feature**: Export notifications to PDF/Excel
4. **Real-time Updates**: WebSocket integration for live updates
5. **Advanced Sorting**: Sort by date, location, or type
6. **Bulk Actions**: Select multiple notifications for batch operations

### Performance Optimizations
1. **Virtual Scrolling**: For handling large datasets
2. **Image Lazy Loading**: Optimize image loading in cards
3. **Infinite Scroll**: Alternative to pagination
4. **Caching Strategy**: More sophisticated caching mechanisms

## Dependencies

### Core Dependencies
- `@nextui-org/react`: UI component library
- `lucide-react`: Icon library
- `next`: React framework
- `react`: Core React library

### Development Dependencies
- `@testing-library/react`: Testing utilities
- `jest`: Testing framework
- `typescript`: Type checking

## File Structure

```
frontend/src/app/(home)/previous/
├── page.tsx              # Main page component
├── __tests__/
│   └── page.test.tsx     # Unit tests
└── README.md             # This documentation
```
