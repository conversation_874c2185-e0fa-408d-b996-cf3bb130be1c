"use client";

import { AlertSection } from "@/components/specific/AlertSection";
import CallUs from "@/components/specific/CallUs";
import { fetcher } from "@/lib/utils";
import { Button } from "@nextui-org/button";
import { ArrowLef<PERSON>, Sir<PERSON> } from "lucide-react";
import Link from "next/link";
import { useContext, useEffect, useState } from "react";
import { UserContext } from "./layout";

type Props = {
  searchParams?: string;
};

export default function Home(props: Props) {
  // Enhanced sample data with emergency types for better fallback
  // Updated to support user-specific notifications
  // Fixed client component implementation
  const sampleOfferAlerts = [
    {
      id: "offer-1",
      user_first_name: "أحمد",
      user_last_name: "السيد",
      location: "القدس - عين علي، شارع مدرسة الثورية - فلسطين",
      created_at: new Date().toISOString(),
      image: "/placeholder.svg?height=200&width=200",
      emergency_type: "O",
      image_count: 1,
    },
    {
      id: "offer-2",
      user_first_name: "محمد",
      user_last_name: "إبراهيم",
      location: "رام الله - شارع الإرسال - فلسطين",
      created_at: new Date().toISOString(),
      image: "/placeholder.svg?height=200&width=200",
      emergency_type: "O",
      image_count: 2,
    },
  ];

  const sampleMedicalAlerts = [
    {
      id: "medical-1",
      user_first_name: "سارة",
      user_last_name: "خالد",
      location: "بيت لحم - شارع المهد - فلسطين",
      created_at: new Date().toISOString(),
      image: "/placeholder.svg?height=200&width=200",
      emergency_type: "M",
      image_count: 1,
    },
    {
      id: "medical-2",
      user_first_name: "فاطمة",
      user_last_name: "أحمد",
      location: "الخليل - البلدة القديمة - فلسطين",
      created_at: new Date().toISOString(),
      image: "/placeholder.svg?height=200&width=200",
      emergency_type: "M",
      image_count: 3,
    },
  ];

  const sampleDangerAlerts = [
    {
      id: "danger-1",
      user_first_name: "عمر",
      user_last_name: "محمود",
      location: "نابلس - شارع فيصل - فلسطين",
      created_at: new Date().toISOString(),
      image: "/placeholder.svg?height=200&width=200",
      emergency_type: "D",
      image_count: 1,
    },
    {
      id: "danger-2",
      user_first_name: "ليلى",
      user_last_name: "حسن",
      location: "جنين - المخيم - فلسطين",
      created_at: new Date().toISOString(),
      image: "/placeholder.svg?height=200&width=200",
      emergency_type: "D",
      image_count: 2,
    },
  ];

  // State for emergency data
  const [offerHelpReqs, setOfferHelpReqs] = useState<{ results: any[] }>({ results: [] });
  const [medicalReqs, setMedicalReqs] = useState<{ results: any[] }>({ results: [] });
  const [dangerReqs, setDangerReqs] = useState<{ results: any[] }>({ results: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch emergency data
  useEffect(() => {
    console.log("🚀 useEffect triggered - starting data fetch");

    const fetchEmergencyData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log("🔄 Attempting to fetch emergency data from API...");

        const fetchData = async (url: string) => {
          try {
            const response = await fetcher(url, null, "GET");
            if (!response.ok) {
              throw new Error(`API request failed with status ${response.status}`);
            }
            const text = await response.text();
            const data = text ? JSON.parse(text) : { results: [] };
            console.log(`✅ Successfully fetched data for ${url}:`, {
              count: data.count || 0,
              resultsLength: data.results?.length || 0
            });
            return data;
          } catch (error) {
            console.error(`❌ Error fetching ${url}:`, error);
            throw error;
          }
        };

        console.log("🌐 Fetching public emergency notifications...");
        // Fetch public notifications (all users)
        const [offerData, medicalData, dangerData] = await Promise.all([
          fetchData("/emergency/?emergency_type=O"),
          fetchData("/emergency/?emergency_type=M"),
          fetchData("/emergency/?emergency_type=D"),
        ]);

        setOfferHelpReqs(offerData);
        setMedicalReqs(medicalData);
        setDangerReqs(dangerData);

        console.log("🎉 Successfully loaded public emergency data from API");
      } catch (error) {
        console.error("❌ Failed to fetch emergency data, using sample data:", error);
        setError("Failed to load emergency data");
        // Use enhanced sample data as fallback
        setOfferHelpReqs({ results: sampleOfferAlerts });
        setMedicalReqs({ results: sampleMedicalAlerts });
        setDangerReqs({ results: sampleDangerAlerts });
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch data immediately on mount
    fetchEmergencyData();
  }, []); // Empty dependency array - run only once on mount



  return (
    <>
      <div className="absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]">
        <div
          id="send-emergency"
          className="max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center"
        >
          <Siren className="absolute top-3 left-3" />
          <h3>أرسل تنبيهًا للطوارئ</h3>
          <p>
            حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب
            مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن.
          </p>
          <Button
            as={Link}
            href="/add-application"
            endContent={<ArrowLeft />}
            className="mx-auto"
            color="danger"
          >
            أرسل إشعار للطوارئ
          </Button>
        </div>
      </div>
      <section id="recieved-emergency" className="mt-[65vh]">
        <h2 className="text-2xl font-bold text-center">
          {isAuthenticated ? "إشعارات الطوارئ الخاصة بك" : "إشعارات الطوارئ المستلمة"}
        </h2>
        {isAuthenticated && (
          <p className="text-center text-gray-600 mt-2 mb-4">
            عرض الإشعارات التي قمت بنشرها
          </p>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل الإشعارات...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-500 mb-4">حدث خطأ في تحميل البيانات</p>
            <p className="text-gray-600">سيتم عرض البيانات التجريبية</p>
          </div>
        ) : null}

        <AlertSection
          data={offerHelpReqs.results}
          heading="طلبات التنبيه حول الإغاثة"
        />
        <AlertSection
          data={medicalReqs.results}
          heading="طلبات التنبيه حول الصحة"
        />
        <AlertSection
          data={dangerReqs.results}
          heading="طلبات التنبيه حول الخطر"
        />
      </section>
      <CallUs />
    </>
  );
}
