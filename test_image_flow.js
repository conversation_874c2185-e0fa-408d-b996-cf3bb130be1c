// Comprehensive Image Upload and Display Test
// Tests the complete image flow from upload to display

const API_BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

async function testImageFlow() {
    console.log('🖼️  Testing Complete Image Upload and Display Flow');
    console.log('==================================================\n');
    
    const results = {
        backendMediaServing: false,
        apiImageUrls: false,
        frontendImageDisplay: false,
        detailViewImages: false,
        uploadFlow: false
    };
    
    // Test 1: Backend Media File Serving
    console.log('1. Testing Backend Media File Serving...');
    try {
        // Test if we can access an existing image directly
        const response = await fetch(`${API_BASE_URL}/media/emergency/images/pngtree-demolished-house-window-tear-down-house-demolition-photo-image_15122860.jpg`);
        
        if (response.ok && response.headers.get('content-type')?.includes('image')) {
            console.log('✅ Backend media files are accessible');
            console.log(`   Content-Type: ${response.headers.get('content-type')}`);
            results.backendMediaServing = true;
        } else {
            console.log('❌ Backend media files not accessible');
            console.log(`   Status: ${response.status}`);
        }
    } catch (error) {
        console.log('❌ Error accessing backend media files:', error.message);
    }
    
    // Test 2: API Image URLs Format
    console.log('\n2. Testing API Image URLs Format...');
    try {
        // Test emergency list endpoint
        const listResponse = await fetch(`${API_BASE_URL}/emergency/`);
        const listData = await listResponse.json();
        
        if (listResponse.ok && listData.results && listData.results.length > 0) {
            const firstEmergency = listData.results[0];
            
            if (firstEmergency.image && firstEmergency.image.startsWith('http://')) {
                console.log('✅ Emergency list returns full image URLs');
                console.log(`   Sample URL: ${firstEmergency.image.substring(0, 60)}...`);
                results.apiImageUrls = true;
            } else {
                console.log('❌ Emergency list returns relative URLs or no images');
                console.log(`   Image field: ${firstEmergency.image}`);
            }
        } else {
            console.log('❌ No emergency data available for testing');
        }
        
        // Test emergency detail endpoint
        if (listData.results && listData.results.length > 0) {
            const emergencyId = listData.results[0].id;
            const detailResponse = await fetch(`${API_BASE_URL}/emergency/${emergencyId}/`);
            const detailData = await detailResponse.json();
            
            if (detailResponse.ok && detailData.images && detailData.images.length > 0) {
                const firstImage = detailData.images[0];
                
                if (firstImage.src && firstImage.src.startsWith('http://')) {
                    console.log('✅ Emergency detail returns full image URLs with "src" field');
                    console.log(`   Sample URL: ${firstImage.src.substring(0, 60)}...`);
                    results.detailViewImages = true;
                } else {
                    console.log('❌ Emergency detail images format incorrect');
                    console.log(`   Image object: ${JSON.stringify(firstImage)}`);
                }
            } else {
                console.log('⚠️  Emergency detail has no images to test');
            }
        }
    } catch (error) {
        console.log('❌ Error testing API image URLs:', error.message);
    }
    
    // Test 3: Frontend Image Display
    console.log('\n3. Testing Frontend Image Display...');
    try {
        const response = await fetch(`${FRONTEND_URL}/`);
        if (response.ok) {
            console.log('✅ Frontend home page accessible');
            results.frontendImageDisplay = true;
        } else {
            console.log('❌ Frontend home page not accessible');
        }
    } catch (error) {
        console.log('❌ Error accessing frontend:', error.message);
    }
    
    // Test 4: Image URL Accessibility
    console.log('\n4. Testing Image URL Accessibility...');
    try {
        const listResponse = await fetch(`${API_BASE_URL}/emergency/`);
        const listData = await listResponse.json();
        
        if (listData.results && listData.results.length > 0) {
            const imageUrl = listData.results[0].image;
            
            if (imageUrl) {
                const imageResponse = await fetch(imageUrl);
                
                if (imageResponse.ok) {
                    console.log('✅ Image URLs are accessible from API responses');
                    console.log(`   Image size: ${imageResponse.headers.get('content-length')} bytes`);
                } else {
                    console.log('❌ Image URLs from API are not accessible');
                    console.log(`   Status: ${imageResponse.status}`);
                }
            }
        }
    } catch (error) {
        console.log('❌ Error testing image URL accessibility:', error.message);
    }
    
    // Test 5: Upload Endpoint
    console.log('\n5. Testing Upload Endpoint Availability...');
    try {
        // Test if upload endpoint is accessible (should require auth)
        const response = await fetch(`${API_BASE_URL}/emergency/create/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });
        
        // Should return 401 (unauthorized) or 400 (bad request), not 404
        if (response.status === 401) {
            console.log('✅ Upload endpoint accessible (requires authentication)');
            results.uploadFlow = true;
        } else if (response.status === 400) {
            console.log('✅ Upload endpoint accessible (validation error expected)');
            results.uploadFlow = true;
        } else {
            console.log(`⚠️  Upload endpoint returned unexpected status: ${response.status}`);
        }
    } catch (error) {
        console.log('❌ Error testing upload endpoint:', error.message);
    }
    
    // Summary
    console.log('\n📊 Image Flow Test Results:');
    console.log('============================');
    
    const totalTests = Object.keys(results).length;
    const passedTests = Object.values(results).filter(Boolean).length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅' : '❌';
        const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        console.log(`${status} ${testName}`);
    });
    
    console.log(`\n📈 Success Rate: ${successRate}% (${passedTests}/${totalTests})`);
    
    // Recommendations
    console.log('\n🔧 Recommendations:');
    console.log('===================');
    
    if (results.backendMediaServing && results.apiImageUrls) {
        console.log('✅ Backend image handling is working correctly');
    } else {
        console.log('❌ Backend needs fixes for image serving or URL generation');
    }
    
    if (results.detailViewImages) {
        console.log('✅ Detail view image format is correct');
    } else {
        console.log('❌ Detail view image format needs fixing');
    }
    
    if (results.frontendImageDisplay) {
        console.log('✅ Frontend is accessible for image display testing');
    } else {
        console.log('❌ Frontend accessibility issues detected');
    }
    
    if (passedTests === totalTests) {
        console.log('\n🎉 ALL TESTS PASSED! Image flow is fully functional!');
    } else {
        console.log('\n⚠️  Some components need attention for complete image functionality');
    }
    
    console.log('\n🧪 Manual Testing Steps:');
    console.log('========================');
    console.log('1. Go to http://localhost:3000/add-application');
    console.log('2. Upload images in the emergency form');
    console.log('3. Submit the form');
    console.log('4. Check home page for image display in cards');
    console.log('5. Click "عرض التفاصيل" to see all images in detail view');
    
    return results;
}

// Run the comprehensive test
testImageFlow().catch(console.error);
