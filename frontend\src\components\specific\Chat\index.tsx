"use client"
import { cn, fetcher } from "@/lib/utils"
import { type MessageChatType, messageChat } from "@/schemas/messages"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@nextui-org/button"
import { Input } from "@nextui-org/input"
import { MessageCircleMore, Minimize2Icon, Send } from "lucide-react"
import { useEffect, useRef, useState } from "react"
import { useCookies } from "react-cookie"
import { Controller, useForm } from "react-hook-form"

type Props = {}
const mockData = [
  { id: 1, userId: 1, message: "مرحباً" },
  { id: 2, userId: 2, message: "أهلاً" },
  { id: 3, userId: 1, message: "كيف حالك؟" },
  {
    id: 4,
    userId: 2,
    message: "أنا بخير، شكراً! ماذا عنك؟",
  },
  {
    id: 5,
    userId: 1,
    message: "أنا بخير أيضاً. أعمل على مشروع حالياً.",
  },
  { id: 6, userId: 2, message: "هذا رائع! ما نوع المشروع؟" },
  {
    id: 7,
    userId: 1,
    message: "إنه تطبيق ويب لإدارة المهام.",
  },
  {
    id: 8,
    userId: 2,
    message: "يبدو مثيراً للاهتمام! هل تستخدم React في ذلك؟",
  },
  {
    id: 9,
    userId: 1,
    message: "نعم، أستخدم React و Node.js. أحب العمل بهما.",
  },
  {
    id: 10,
    userId: 2,
    message: "وأنا كذلك! أستخدمهما في معظم مشاريعي أيضاً.",
  },
  {
    id: 11,
    userId: 1,
    message: "هل لديك أي نصائح لتحسين الأداء في React؟",
  },
  {
    id: 12,
    userId: 2,
    message: "بالطبع! استخدم React.memo و React.useMemo للحسابات الثقيلة.",
  },
  {
    id: 13,
    userId: 1,
    message: "شكراً! يبدو ذلك مفيداً. سأجربه.",
  },
  {
    id: 14,
    userId: 2,
    message: "على الرحب والسعة. أخبرني إذا احتجت إلى أي مساعدة.",
  },
  { id: 15, userId: 1, message: "سأفعل. شكراً لدعمك!" },
  {
    id: 16,
    userId: 2,
    message: "لا مشكلة! ما التقنيات الأخرى التي تستخدمها؟",
  },
  {
    id: 17,
    userId: 1,
    message: "أستخدم أيضاً PostgreSQL لإدارة قاعدة البيانات.",
  },
  {
    id: 18,
    userId: 2,
    message: "اختيار رائع! PostgreSQL قوي جداً للبيانات العلاقية.",
  },
  {
    id: 19,
    userId: 1,
    message: "بالتأكيد! تعلمت الكثير أثناء العمل على هذا المشروع.",
  },
  {
    id: 20,
    userId: 2,
    message: "هذه أفضل ميزة في بناء المشاريع. تتعلم الكثير!",
  },
]

export function Chat({}: Props) {
  const currentUserId = 1
  const [cookies] = useCookies()
  const bottomRef = useRef<HTMLDivElement>(null)
  const [open, setOpen] = useState<boolean>(false)
  const access = cookies["access"]
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<MessageChatType>({
    resolver: zodResolver(messageChat),
    defaultValues: {
      message: "",
    },
  })

  const [messages, setMessages] = useState<typeof mockData>(mockData)

  const scrollToBottom = () => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    if (access) {
      const fetchMessages = async () => {
        try {
          const res = await fetcher("/chats/messages/", null, "GET", access)
          const data = await res.json()
          // Uncomment when API is ready
          // setMessages(data["results"]);
          scrollToBottom()
        } catch (error) {
          console.error("Failed to fetch messages:", error)
        }
      }

      fetchMessages()
    }
  }, [access])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const onSubmit = (data: MessageChatType) => {
    setMessages((prev) => [...prev, { id: Date.now(), userId: currentUserId, message: data.message }])
    reset()
  }

  return (
    <>
      {open ? (
        <div className="h-[500px] w-[400px] bg-slate-200 fixed bottom-0 right-0 rounded-xl overflow-clip flex flex-col">
          <header className="flex justify-between items-center bg-blue-600 text-white py-3 px-2">
            <h2 className="font-semibold text-lg">خدمة العملاء</h2>
            <Button className="bg-black/75 p-1 rounded-full min-w-8" onPress={() => setOpen(false)}>
              <Minimize2Icon className="text-white size-4" />
            </Button>
          </header>
          <div className="flex-1 h-[23.5rem] overflow-y-auto">
            {messages?.length === 0 ? (
              <div className="grid place-content-center h-full">
                <p>لا يوجد لديك رسائل</p>
              </div>
            ) : (
              <div className="flex flex-col gap-2 pt-2 justify-end">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={cn("flex flex-col mb-2", msg.userId === currentUserId ? "items-start" : "items-end")}
                  >
                    <p
                      className={cn(
                        "max-w-[250px] px-4 py-2 rounded-xl text-white text-sm",
                        msg.userId === currentUserId ? "bg-red-500 rounded-tl-none" : "bg-black/25 rounded-tr-none",
                      )}
                    >
                      {msg.message}
                    </p>
                  </div>
                ))}

                <div ref={bottomRef} />
              </div>
            )}
          </div>
          <form className="flex gap-2 px-2 border-t-1 pt-3 pb-2" onSubmit={handleSubmit(onSubmit)}>
            <Controller
              name="message"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="أدخل رسالتك"
                  isInvalid={!!errors.message}
                  errorMessage={errors.message?.message}
                />
              )}
            />
            <Button type="submit" disabled={isSubmitting} className="min-w-8 bg-blue-600">
              <Send className="text-white" />
            </Button>
          </form>
        </div>
      ) : (
        <Button
          onPress={() => setOpen(true)}
          className="fixed top-[92vh] right-2 w-16 h-16 px-0 bg-blue-600 text-white rounded-full text-xl font-bold grid place-content-center"
        >
          <MessageCircleMore className="size-8" />
        </Button>
      )}
    </>
  )
}
